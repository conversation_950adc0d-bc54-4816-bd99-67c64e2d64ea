
import React, { useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { buildNotificationSchema } from '../schemas/notificationsSchema';
import styles from './NotificationsTab.module.scss'; // <- use the same stylesheet
import clsx from 'clsx';
import { Fade, Tooltip } from '@mui/material';
import { useBuyerSettingStore, useGlobalStore, usePostSaveUserNotificationSetting } from '@bryzos/giss-ui-library';
import { navigatePage } from 'src/renderer2/helper';
import { routes } from 'src/renderer2/common';


const NotificationsTab: React.FC<any> = ({
  setActiveTab,
  setSaveFunctions
}: any) => {
  const currentUserType = 'buyer';
  const { referenceNotificationSettings , userNotificationData} = useBuyerSettingStore()
  const schema = buildNotificationSchema(referenceNotificationSettings);
  const {setShowLoader} = useGlobalStore()
  const { mutateAsync: saveUserNotification } = usePostSaveUserNotificationSetting();

  const methods = useForm<{ data: any }>({
    resolver: yupResolver(schema),
    defaultValues: { data: {} },
    mode: 'onBlur',
  });

  const { setValue, handleSubmit, register, watch, reset, formState: { errors , isSubmitting , isDirty , isValid } } = methods;

  const isButtonDisabled = !isValid || isSubmitting || !isDirty;

    useEffect(() => {
      setSaveFunctions({
          onSave: () => handleSubmit(handleSave)(),
          onSaveAndNext: () => handleSubmit(handleSaveAndNext)(),
          onSaveAndExit: () => handleSubmit(handleSaveAndExit)(),
          isDisabled: isButtonDisabled,
      });
  }, [isButtonDisabled, handleSubmit]);


  useEffect(() => {
    if (referenceNotificationSettings && userNotificationData) {
      console.log("referenceNotificationSettings", referenceNotificationSettings)
      console.log("userNotificationData", userNotificationData)
      Object.values(referenceNotificationSettings).forEach((events) => {
        events.forEach((event) => {
          const allowed =
            !event.to_user_type ||
            event.to_user_type === currentUserType ||
            event.to_user_type === 'user';

          if (!allowed || !event.reference_column) return;

          const saved = userNotificationData[event.reference_column] || {};

          setValue(`data.${event.reference_column}`, {
            email: event.is_email_required ? true : saved.email ?? false,
            text: event.is_text_required ? true : saved.text ?? false,
            pusher: event.is_pusher_required ? true : saved.pusher ?? false,
          });
        });
      });
    }
  }, [referenceNotificationSettings, userNotificationData, currentUserType, setValue]);

  const handleSave = async (data: any) => {
    setShowLoader(true)
    try{
      const cleanedPayload = {
        data: Object.fromEntries(
          Object.entries(data.data).filter(([key]) => key !== 'null' && key)
        ),
      };
      await saveUserNotification(cleanedPayload)
      reset(data); 
    }catch(error){
      console.error("error", error)
    }finally{
      setShowLoader(false)
    }
  };

  const handleSaveAndNext = async (data: { data: any }) => {
    await handleSave(data)
    setActiveTab('SUBSCRIPTION')
  }

  const handleSaveAndExit = async (data: { data: any }) => {
    await handleSave(data)
    navigatePage(location.pathname, { path: routes.homePage })
  }


  console.log("isvalid", isValid)
  console.log("isSubmitting", isSubmitting)
  console.log("isDirty", isDirty)
  console.log("isButtonDisabled", isButtonDisabled)

  return (
    <FormProvider {...methods}>
      <div className={styles.notificationMainContainer}>
        <div className={styles.notificationHeader}>
          Some notifications are required and unable to opt-out. Select the notifications you would
          like to receive and how you would like receive them: by
          <span className={styles.methodText}> Text <span className={clsx(styles.methodIcon, styles.textIcon)}>T</span>&nbsp;</span>,
          <span className={styles.methodText}> Email <span className={clsx(styles.methodIcon, styles.emailIcon)}>E</span>&nbsp;</span>, or
          <span className={styles.methodText}> Desktop <span className={clsx(styles.methodIcon, styles.desktopIcon)}>D</span>&nbsp;</span>.
        </div>

        <div className={styles.notificationContainer}>
          {(referenceNotificationSettings && userNotificationData) && (
            <>
              {(() => {
                // Define the custom order for categories
                const categoryOrder = [
                  { key: "USER ACCOUNT", displayName: 'USER ACCOUNT' },
                  { key: "ORDER UPDATES", displayName: 'ORDER UPDATES' },
                  { key: "APP FEATURES", displayName: 'APP FEATURES' },
                ];

                return categoryOrder.map(({ key, displayName }) => {
                  const events = referenceNotificationSettings[key];
                  if (!events || events.length === 0) return null;

                  return (
                    <div key={key} className={styles.notificationSection}>
                      <div className={styles.notificationSectionTitle}>{displayName}</div>
                      <div className={styles.notificationSectionContent}>
                        {events.map((event) => {
                          const allowed =
                            !event.to_user_type ||
                            event.to_user_type === currentUserType ||
                            event.to_user_type === 'user';

                          if (!allowed || !event.reference_column) return null;

                          const value = watch(`data.${event.reference_column}`) || {};

                          return (
                            <div key={event.reference_column} className={styles.notificationItem}>
                              <Tooltip
                                title={event.tooltip}
                                placement={"top"}
                                disableInteractive
                                TransitionComponent={Fade}
                                TransitionProps={{ timeout: 100 }}
                                classes={{
                                  tooltip: "inputQtyTooltip",
                                }}
                              >
                                <span>{event.display_title}</span>
                              </Tooltip>
                              {/* {event.tooltip && (
                                <div className={styles.notificationTooltip}>{event.tooltip}</div>
                              )} */}
                              <div className={styles.notificationToggle}>
                                
                              <div 
                                  className={clsx(styles.notificationCheckbox, styles.textType, value.text && styles.checked)}
                                  onClick={() => !true && setValue(`data.${event.reference_column}.text`, !value.text , {
                                    shouldDirty: true,
                                  })}
                                  style={{ cursor: true ? 'not-allowed' : 'pointer' }}
                                >
                                  T
                                </div>
                                <div 
                                  className={clsx(styles.notificationCheckbox, styles.emailType, value.email && styles.checked)}
                                  onClick={() => !event.is_email_required && setValue(`data.${event.reference_column}.email`, !value.email , {
                                    shouldDirty: true,
                                  })}
                                  style={{ cursor: event.is_email_required ? 'not-allowed' : 'pointer' }}
                                >
                                  E
                                </div>

                                <div 
                                  className={clsx(styles.notificationCheckbox, styles.desktopType, value.pusher && styles.checked)}
                                  onClick={() => !event.is_pusher_required && setValue(`data.${event.reference_column}.pusher`, !value.pusher , {
                                    shouldDirty: true,
                                  })}
                                  style={{ cursor: event.is_pusher_required ? 'not-allowed' : 'pointer' }}
                                >
                                  D
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                });
              })()}
            </>
          )}
        </div>
      </div>
    </FormProvider>
  );
};

export default NotificationsTab;
