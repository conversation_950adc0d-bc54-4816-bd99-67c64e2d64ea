import React, { useEffect, useRef } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import styles from './SubscribeUserTable.module.scss';
import {
  subscribeUserTableSchema,
  type SubscribeUserTableFormData,
  type User
} from './SubscribeUserTable.schema';
import { useSubscriptionStore } from '@bryzos/giss-ui-library';

interface SubscribeUserTableProps {
  initialUsers?: User[];
  onUserUpdate?: (users: User[]) => void;
  onSave?: (data: SubscribeUserTableFormData) => void;
}

const SubscribeUserTable: React.FC<SubscribeUserTableProps> = ({
  onUserUpdate,
  onSave
}) => {
  const { userList, uploadUserList, setUploadUserList, userSubscription } = useSubscriptionStore();

  // Store original data for comparison
  const originalDataRef = useRef<User[]>([]);

  // Get license counts from subscription store
  const totalLicenses = 3;


  const {
    control,
    handleSubmit,
    formState: { errors, isDirty, isSubmitted },
    watch,
    setValue,
    clearErrors
  } = useForm<SubscribeUserTableFormData>({
    resolver: yupResolver(subscribeUserTableSchema),
    mode: 'onSubmit', // Only validate on submit
    reValidateMode: 'onSubmit', // Keep validation only on submit even after first submit
    defaultValues: {
      users: []
    }
  });

  // Set initial user data from store
  useEffect(() => {
    if (userList?.length > 0) {
      const transformedUsers = userList.map((user: any) => ({
        ...user,
        isExisting: true
      }));

      // Store deep copy of original data for comparison
      originalDataRef.current = JSON.parse(JSON.stringify(transformedUsers));

      // Always add placeholder row at the end
      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        license: 'Unassigned',
        action: '',
        status: 'Active',
        isExisting: false,
        isRemoved: false
      };

      setValue('users', [...transformedUsers, placeholderUser]);
    } else {
      // If no existing users, just show placeholder row
      originalDataRef.current = [];

      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        license: 'Unassigned',
        action: '',
        status: 'Active',
        isExisting: false,
        isRemoved: false
      };

      setValue('users', [placeholderUser]);
    }
  }, [userList, setValue]);

  // Handle uploaded users from store
  useEffect(() => {
    if (uploadUserList?.length > 0) {
      const transformedUploadedUsers = uploadUserList.map((user: any) => ({
        id: `new-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, // Generate temporary ID for form handling
        user_name: user.user_name || '',
        email_id: user.email_id || '',
        license: user.license ? 'Assigned' : 'Unassigned', // Convert boolean to string
        action: user.status || '', // Use status as action or empty string
        status: user.status || 'Active',
        isExisting: false, // Treat uploaded users as new users (placeholder rows)
        isRemoved: false
      }));

      // Don't update original data reference since these are treated as new users
      // originalDataRef.current remains unchanged

      // Keep the final placeholder row at the end
      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        license: 'Unassigned',
        action: '',
        status: 'Active',
        isExisting: false,
        isRemoved: false
      };

      setValue('users', [...transformedUploadedUsers, placeholderUser], { shouldDirty: true });
      setUploadUserList(null);
    }
  }, [uploadUserList, setValue, setUploadUserList]);

  const { fields, update, append } = useFieldArray({
    control,
    name: 'users'
  });

  const watchedUsers = watch('users') || [];

  // Calculate current assigned licenses from watched users
  const currentAssignedLicenses = watchedUsers.filter(user => user.license === 'Assigned').length;

  // Calculate how many more licenses can be assigned
  const remainingLicenses = totalLicenses - currentAssignedLicenses;

  // Add new placeholder row when email field is filled and blurred
  const handleEmailBlur = (index: number) => {
    const currentUser = watchedUsers[index];

    // Only add new placeholder if this is a placeholder row that now has both username and email
    if (currentUser && !currentUser.isExisting &&
        currentUser.user_name?.trim() && currentUser.email_id?.trim()) {

      // Check if there's already another empty placeholder row
      const hasEmptyPlaceholder = watchedUsers.some(user =>
        !user.isExisting && !user.user_name?.trim() && !user.email_id?.trim()
      );

      if (!hasEmptyPlaceholder) {
        const newPlaceholder: User = {
          id: `new-${Date.now()}`,
          user_name: '',
          email_id: '',
          license: 'Unassigned',
          action: '',
          status: 'Active',
          isExisting: false,
          isRemoved: false
        };

        append(newPlaceholder);
      }
    }
  };

  // Handle license changes
  const handleLicenseChange = (index: number, newLicense: 'Assigned' | 'Unassigned') => {
    if (watchedUsers[index]) {
      const currentUser = watchedUsers[index];
      
      // Prevent assigning more licenses than available
      if (newLicense === 'Assigned' && currentUser.license === 'Unassigned') {
        if (remainingLicenses <= 0) {
          // Cannot assign more licenses
          return;
        }
      }
      
      const updatedUser = { ...currentUser, license: newLicense };
      update(index, updatedUser);
      onUserUpdate?.(watchedUsers as User[]);
    }
  };

  // Handle action changes
  const handleActionChange = (index: number, action: string) => {
    if (!watchedUsers[index]) return;

    const updatedUser = {
      ...watchedUsers[index],
      action,
      status: action,
      // Mark as removed if action is "Remove", but keep in data for backend
      isRemoved: action === 'Remove'
    };

    console.log(`Action changed for user ${updatedUser.user_name}: ${action}, isRemoved: ${updatedUser.isRemoved}`);

    update(index, updatedUser);
    onUserUpdate?.(watchedUsers as User[]);
  };



  // Utility functions
  const isPlaceholderRow = (user: any): boolean => {
    return !user.isExisting && !user.user_name?.trim() && !user.email_id?.trim();
  };

  const isFieldDisabled = (user: any, field: 'user_name' | 'email_id' | 'license' | 'action'): boolean => {
    if (user.isExisting) return false;

    // For new users, username and email are always enabled for tab navigation
    if (field === 'user_name' || field === 'email_id') return false;

    // For new users, disable action buttons and license until username is filled
    if (field === 'action' || field === 'license') {
      return !user.user_name?.trim();
    }

    return false;
  };

  // Check if license field should be disabled due to license limit
  const isLicenseDisabled = (user: any): boolean => {
    // If this is an existing user with an assigned license, allow them to keep it
    if (user.isExisting && user.license === 'Assigned') {
      return false;
    }
    
    // If trying to assign a license and no more are available, disable
    if (user.license === 'Unassigned' && remainingLicenses <= 0) {
      return true;
    }
    
    return false;
  };

  // Utility function to compare if user data has changed
  const hasUserChanged = (currentUser: any, originalUser: User | undefined): boolean => {
    if (!originalUser) return true; // New user

    return (
      currentUser.user_name !== originalUser.user_name ||
      currentUser.email_id !== originalUser.email_id ||
      currentUser.license !== originalUser.license ||
      currentUser.action !== originalUser.action ||
      currentUser.status !== originalUser.status
    );
  };

  // Form submission with payload filtering and change detection
  const onSubmit = (data: SubscribeUserTableFormData) => {
    console.log("on submit called");

    const processedUsers: any[] = [];

    (data.users || []).forEach(user => {
      const userTyped = user as User;

      // Skip empty placeholder rows (but not removed users)
      if (!userTyped.isExisting && !userTyped.isRemoved && (!userTyped.user_name?.trim() || !userTyped.email_id?.trim())) {
        return;
      }

      if (userTyped.isExisting) {
        // For existing users, find original and check if changed OR if removed
        const originalUser = originalDataRef.current.find(orig => orig.id === userTyped.id);

        if (userTyped.isRemoved || hasUserChanged(userTyped, originalUser)) {
          // Include id for existing users and send data (including removed ones)
          processedUsers.push({
            id: userTyped.id,
            user_name: userTyped.user_name,
            email_id: userTyped.email_id,
            license: userTyped.license,
            action: userTyped.action,
            status: userTyped.status
          });
        }
      } else {
        // For new users, don't include id and only include if both fields are filled
        // Skip new users with "Remove" action - they don't exist yet, so nothing to remove
        if (userTyped.user_name?.trim() && userTyped.email_id?.trim() && userTyped.action !== 'Remove') {
          processedUsers.push({
            user_name: userTyped.user_name.trim(),
            email_id: userTyped.email_id.trim(),
            license: userTyped.license,
            action: userTyped.action,
            status: userTyped.status
          });
        }
      }
    });

    const payload = { users: processedUsers };
    console.log('Form submitted with filtered and changed payload:', payload);
    onSave?.(payload);
  };

  // Render editable cell for existing users
  const renderEditableCell = (index: number, field: 'user_name' | 'email_id') => {
    const fieldName = `users.${index}.${field}` as const;
    const error = errors.users?.[index]?.[field];
    const user = watchedUsers[index];

    // Only show errors after form submission attempt
    const shouldShowError = error && isSubmitted;

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Tab') {
        // Let normal tab behavior work - the useEffect will handle adding new placeholder rows
        return;
      }
    };

    return (
      <div className={styles.cellContainer}>
        <Controller
          name={fieldName}
          control={control}
          render={({ field: controllerField }) => (
            <input
              type="text"
              {...controllerField}
              onChange={(e) => {
                controllerField.onChange(e);
                // Clear errors when user starts typing to prevent blur validation
                if (shouldShowError) {
                  clearErrors(`users.${index}.${field}`);
                }
              }}
              onBlur={() => {
                controllerField.onBlur();
                // Add new placeholder row when email field is blurred and filled
                if (field === 'email_id') {
                  handleEmailBlur(index);
                }
              }}
              onKeyDown={handleKeyDown}
              disabled={isFieldDisabled(user, field)}
              className={`${styles.editInput} ${shouldShowError ? styles.error : ''} ${
                isPlaceholderRow(user) ? styles.placeholder : ''
              } ${isFieldDisabled(user, field) ? styles.disabled : ''}`}
              placeholder={field === 'user_name' ? 'Enter user name' : 'Enter email'}
              tabIndex={0}
            />
          )}
        />
        {shouldShowError && (
          <span className={styles.errorMessage}>
            {error.message}
          </span>
        )}
      </div>
    );
  };

  console.log("errors",errors)

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={styles.formContainer}>
      <div className={styles.tableContainer}>
        <table className={styles.userTable}>
          <thead>
            <tr className={styles.tableHeader}>
              <th>USER NAME</th>
              <th>Email</th>
              <th>LICENSE</th>
              <th>ACTION</th>
              <th>STATUS</th>
            </tr>
          </thead>
          <tbody>
            {fields.map((field, index) => {
              const user = watchedUsers[index];
              const isPlaceholder = isPlaceholderRow(user);

              // Hide removed users from display
              if ((user as User)?.isRemoved) {
                return null;
              }

              return (
                <tr
                  key={field.id}
                  className={`${styles.tableRow} ${isPlaceholder ? styles.placeholderRow : ''}`}
                >
                  <td className={styles.tableCell}>
                    {renderEditableCell(index, 'user_name')}
                  </td>
                  <td className={styles.tableCell}>
                    {renderEditableCell(index, 'email_id')}
                  </td>
                  <td className={styles.tableCell}>
                    <div className={styles.licenseCellContainer}>
                      <select
                        value={user?.license || 'Unassigned'}
                        onChange={(e) => handleLicenseChange(index, e.target.value as 'Assigned' | 'Unassigned')}
                        disabled={isFieldDisabled(user, 'license') || isLicenseDisabled(user)}
                        className={`${styles.dropdown} ${
                          user?.license === 'Assigned' ? styles.assigned : styles.unassigned
                        } ${(isFieldDisabled(user, 'license') || isLicenseDisabled(user)) ? styles.disabled : ''}`}
                      >
                        <option value="Assigned">Assigned</option>
                        <option value="Unassigned">Unassigned</option>
                      </select>
                    </div>
                  </td>
                  <td className={styles.tableCell}>
                    <select
                      value={user?.action || 'Select Action'}
                      onChange={(e) => {
                        if (e.target.value !== 'Select Action') {
                          handleActionChange(index, e.target.value);
                        }
                      }}
                      disabled={isFieldDisabled(user, 'action')}
                      className={`${styles.dropdown} ${isFieldDisabled(user, 'action') ? styles.disabled : ''}`}
                    >
                      <option value="Select Action">Select Action</option>
                      <option value="Invite">Invite</option>
                      <option value="Remove">Remove</option>
                    </select>
                  </td>
                  <td className={styles.tableCell}>
                    <span className={styles.status}>
                      {user?.status || 'Active'}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      <div className={styles.saveButtonContainer}>
        <button
          type="submit"
          disabled={!isDirty}
          onClick={handleSubmit(onSubmit)}
          className={`${styles.saveButton} ${!isDirty ? styles.disabled : ''}`}
        >
          Save Changes
        </button>
      </div>
    </form>
  );
};

export default SubscribeUserTable;
