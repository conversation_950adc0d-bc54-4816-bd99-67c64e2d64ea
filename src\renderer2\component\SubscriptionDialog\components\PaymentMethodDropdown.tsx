import React from 'react';
import { useFormContext } from 'react-hook-form';
import { PAYMENT_METHODS } from 'src/renderer2/common';

interface PaymentMethodDropdownProps {
  onPaymentMethodChange: (method: string) => void;
}

const PaymentMethodDropdown: React.FC<PaymentMethodDropdownProps> = ({ onPaymentMethodChange }) => {
  const { register, watch } = useFormContext();
  const selectedPaymentMethod = watch('paymentMethod');

  const handlePaymentMethodChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const method = e.target.value;
    onPaymentMethodChange(method);
  };

  return (
    <div>
      <label>CHOOSE METHOD OF PAYMENT</label>
      <select
        {...register('paymentMethod')}
        onChange={handlePaymentMethodChange}
        className="payment-method-select"
      >
        <option value="">Select payment method</option>
        <option value={PAYMENT_METHODS.CARD}>CREDIT / DEBIT CARD</option>
        <option value={PAYMENT_METHODS.ACH}>BANK TRANSFER</option>
      </select>
    </div>
  );
};

export default PaymentMethodDropdown; 