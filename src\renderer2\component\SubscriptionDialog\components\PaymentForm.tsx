import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import PaymentMethodDropdown from './PaymentMethodDropdown';
import CreditCardForm from './CreditCardForm';
import BankTransferForm from './AchPaymentForm';
import { PAYMENT_METHODS } from 'src/renderer2/common';
import AchPaymentForm from './AchPaymentForm';

interface PaymentFormProps {
  onPaymentMethodChange: (method: string) => void;
  selectedPaymentMethod: string;
  cardComplete: {
    cardNumber: boolean;
    cardExpiry: boolean;
    cardCvc: boolean;
  };
  cardError: {
    cardNumber: string | null;
    cardExpiry: string | null;
    cardCvc: string | null;
  };
  stripeError: string | null;
  onCardChange: (event: any, fieldName: 'cardNumber' | 'cardExpiry' | 'cardCvc') => void;
}

const PaymentForm: React.FC<PaymentFormProps> = ({ 
  onPaymentMethodChange, 
  selectedPaymentMethod,
  cardComplete,
  cardError,
  stripeError,
  onCardChange
}) => {
  return (
    <div className="payment-form">
      <PaymentMethodDropdown onPaymentMethodChange={onPaymentMethodChange} />
      
      {selectedPaymentMethod === PAYMENT_METHODS.CARD && (
        <CreditCardForm 
          cardComplete={cardComplete}
          cardError={cardError}
          stripeError={stripeError}
          onCardChange={onCardChange}
        />
      )}
      
      {selectedPaymentMethod === PAYMENT_METHODS.ACH && (
        <AchPaymentForm />
      )}
    </div>
  );
};

export default PaymentForm; 