import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { companySchema, settingSchema } from '../schemas';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import { CustomMenu } from '../../CustomMenu';
import { watch } from 'fs';
import { Autocomplete, ClickAwayListener, Dialog, Select, TextField } from '@mui/material';
import { Watch } from '@mui/icons-material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import CustomAddressComponent from '../components/CustomAddressComponent';
import axios from 'axios';
import { useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { useImmer } from 'use-immer';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import { useQueryClient } from '@tanstack/react-query';
import { prefixUrl, reactQueryKeys, routes } from 'src/renderer2/common';
import { navigatePage } from 'src/renderer2/helper';
import { ReactComponent as IconUpload } from '../../../../assets/New-images/icon-upload.svg';
import { v4 as uuidv4 } from 'uuid';
import DropdownSave from '../components/DropdownSave';
import { EmailTagInputField } from 'src/renderer2/component/EmailTagInput';
import SingleStateSelector from '../components/StateSelector/SingleStateSelector';

interface InputFocusState {
    parentCompanyName: boolean;
    companyDBAName: boolean;
    companyType: boolean;
    billingContactName: boolean;
    billingContactEmail: boolean;
    sendInvoicesTo: boolean;
    sendRemittancesTo: boolean;
    companyAddress: boolean;
    companyW9Form: boolean;
}

const CompanyTab: React.FC<{ setActiveTab: any, setSaveFunctions: any}> = ({ setActiveTab , setSaveFunctions }) => {
    const {
        register,
        handleSubmit,
        clearErrors,
        setError,
        setValue,
        reset,
        watch,
        control,
        getValues,
        trigger,
        resetField,
        formState: { errors, dirtyFields, isDirty, isValid, isSubmitting  },
        getFieldState,
      } = useForm({
        resolver: yupResolver(companySchema),
        mode: 'onSubmit',
      });
    
    const selectCompanyHQAddressRef = useRef(null);
    const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
        parentCompanyName: false,
        companyDBAName: false,
        companyType: false,
        billingContactName: false,
        billingContactEmail: false,
        sendInvoicesTo: false,
        sendRemittancesTo: false,
        companyAddress: false,
        companyW9Form: false,
    });
    const {referenceData , setShowLoader, userData}: any = useGlobalStore();
    const [states, setStates] = useState<any[]>([]);
    const { mutateAsync: saveUserSettings } = useSaveUserSettings();
    const { buyerSetting }: any = useBuyerSettingStore();
    const companyW9FormRef = useRef<HTMLInputElement>(null);
    const isButtonDisabled =  isSubmitting || !isDirty;
    const [isAddressContainerClicked, setIsAddressContainerClicked] = useState(false);
    const [isStateSelectorFocused, setIsStateSelectorFocused] = useState(false);
    const addressContainerRef = useRef<HTMLDivElement>(null);
    const line1InputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if(buyerSetting) {
            setValue('parentCompanyName', buyerSetting?.company_name || '');
            setValue('companyType', buyerSetting?.company_type || '');
            setValue('companyAddress', {
              line1: buyerSetting?.company_address?.line1 || '',
              line2: buyerSetting?.company_address?.line2 || '',
              city: buyerSetting?.company_address?.city || '',
              state: buyerSetting?.company_address?.state_id || '',
              stateCode: buyerSetting?.company_address?.state_code || '',
              zip: buyerSetting?.company_address?.zip || '',
            });
            setValue('companyW9Form', {
                cerificate_url_s3: buyerSetting?.company_w9_pdf_url || '',
                file_name: buyerSetting?.company_w9_pdf_name || '',
            });
            setValue('billingContactName', buyerSetting?.ap_contact_name || '');
            setValue('billingContactEmail', buyerSetting?.ap_email_id || '');
            setValue('sendInvoicesTo', buyerSetting?.send_invoices_to || '');
            setValue('sendRemittancesTo', '<EMAIL>');
        }
    }, [buyerSetting]);

    useEffect(() => {
        setSaveFunctions({
            onSave: () => handleSubmit(handleSaveCompany)(),
            isDisabled: isButtonDisabled,
        });
    }, [isButtonDisabled, handleSubmit]);


    useEffect(() => {   
        setTimeout(() => {
            const parentCompanyNameInput = document.getElementById('parentCompanyName');
            if (parentCompanyNameInput) {
                parentCompanyNameInput.focus();
            }
        }, 100)
    }, []);


        useEffect(() => {
            handleStateZipValidation('companyAddress.zip', 'companyAddress.state')
        }, [watch('companyAddress.state'), watch('companyAddress.zip')])


    useEffect(() => {
        if(referenceData?.ref_states) {
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    useEffect(() => {
        if (isAddressContainerClicked && line1InputRef.current) {
          // Small delay to ensure the input is rendered
          setTimeout(() => {
            line1InputRef.current?.focus();
          }, 0);
        }
      }, [isAddressContainerClicked]);


    const handleInputFocus = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: true,
        }));
    };

    const handleInputBlur = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: false,
        }));
    };

    const companyTypes = [
        { title: 'Fabricator', value: 'Fabricator' },
        { title: 'Constructor', value: 'Constructor' },
        { title: 'Distributor', value: 'Distributor' },
        { title: 'OEM', value: 'OEM' },
    ];

    const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
        try {
            if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
                const payload = {
                data: {
                    state_id: Number(getValues(stateCode)),
                    zip_code: parseInt(getValues(zipCode)),
                },
            };
            const checkStateZipResponse = await axios.post(
                import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
                payload
            );
            if (checkStateZipResponse.data.data === true) {
                clearErrors([stateCode, zipCode]);
                return true;
            } else {
                setError(stateCode, { message: "The zip code and state code do not match" });
                setError(zipCode, { message: "The zip code and state code do not match" });
                return false;
            }
        }
     } catch (error) {
            console.error(error)
        }
    };

    // Remove the useEffect that runs on every change
    // Instead, we'll handle validation in the onBlur events

    const handleSaveCompany = async (data: any) => {
        try{
            setShowLoader(true)
            const payload = {
                company_name: data.parentCompanyName,
                company_type: data.companyType,
                company_address: {
                    line1: data.companyAddress.line1,
                    line2: data.companyAddress.line2?.trim() || null,
                    city: data.companyAddress.city,
                    state_id: Number(data.companyAddress.state),
                    zip: data.companyAddress.zip
                },
                company_w9_pdf_name: data.companyW9Form.file_name,
                company_w9_pdf_url: data.companyW9Form.cerificate_url_s3,
                ap_contact_name: data.billingContactName,
                ap_email_id: data.billingContactEmail,
                send_invoices_to: data.sendInvoicesTo,
                send_remittances_to: data.sendRemittancesTo,
            }
            await saveUserSettings({ route: 'user/buyer/settings/company', data: payload })
            // queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
            setShowLoader(false)
            setTimeout(() => {
                reset(data); 
            }, 100)
        }catch(err){
            console.error(err)
            setShowLoader(false)
        }
    }

    const irsW9FormEditHandler = () => {
        companyW9FormRef?.current?.click();
    }

    const uploadW9Form = async (event: any) => {
        const file = event.target.files[0];
        setValue('companyW9Form.file_name', file.name , { shouldValidate: true })
        setValue('companyW9Form.file', file)

        if (event.target.files.length !== 0) {
            let index = file.name.length - 1;
            for (; index >= 0; index--) {
                if (file.name.charAt(index) === '.') {
                    break;
                }
            }
            const ext = file.name.substring(index + 1, file.name.length);

            const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.buyerCompanyW9Prefix + '-' + uuidv4() + '.' + ext;

            const payload = {
                data: {
                    "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_IRS_W9_BUCKET_NAME,
                    "object_key": objectKey,
                    "expire_time": 300

                }
            }
            let setIRSUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
            axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
                .then(response => {
                    const signedUrl = response.data.data;
                    axios.put(signedUrl, file)
                        .then(response => {
                            if (response.status === 200) {
                                setValue('companyW9Form.cerificate_url_s3', setIRSUrl , { shouldValidate: true , shouldDirty: true})
                            }
                        })
                        .catch(error => {
                            console.error(error);
                            setShowLoader(false);
                        }
                        );
                })
                .catch(error => {
                    console.error(error);
                    setShowLoader(false);
                }
                );

        }
    }

    const handleShipmentAddressContainerClickAway = () => {
        handleInputBlur('companyAddress')
        if (!isStateSelectorFocused) {
          if(!(errors?.companyAddress?.line1 || errors?.companyAddress?.line2 || errors?.companyAddress?.city || errors?.companyAddress?.state || errors?.companyAddress?.zip || errors?.companyAddress?.stateCode)){
            setIsAddressContainerClicked(false)
          }else{
            setIsAddressContainerClicked(true)
          }
        }
      }
    
      // Custom clickaway handler
      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (!isAddressContainerClicked) return;
          
          const target = event.target as HTMLElement;
          
          // Check if click is inside the address container
          if (addressContainerRef.current && addressContainerRef.current.contains(target)) {
            return;
          }
          
          // Check if click is inside any state selector
          const stateSelectorElement = document.querySelector('[data-state-selector]');
          if (stateSelectorElement && stateSelectorElement.contains(target)) {
            return;
          }
          
          // If we get here, the click was outside both the container and state selector
          handleShipmentAddressContainerClickAway();
        };
    
        // Add event listener when address container is clicked
        if (isAddressContainerClicked) {
          document.addEventListener('mousedown', handleClickOutside);
        }
    
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isAddressContainerClicked]);


      useEffect(() => {
        if(errors?.companyAddress){
            setIsAddressContainerClicked(true)
        }
      }, [errors])
    
    return (
        <div className={styles.tabContent} ref={selectCompanyHQAddressRef}>
            <div className={styles.formContainer}>
                {/* PARENT COMPANY NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label 
                        // className={clsx(isInputFocused.parentCompanyName && styles.focusLbl)} 
                        htmlFor="parentCompanyName">
                            Company Name
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.inputCreateAccount,styles.companyNameInput)}
                        id='parentCompanyName'
                        tabIndex={0} 
                        onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: false,
                            }));
                        }}
                        >{watch('parentCompanyName')}</span>
                    </span>
                </div>

                {/* COMPANY TYPE */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyType && styles.focusLbl)} htmlFor="companyType">
                        company type
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <CustomMenu
                            onfocus={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: true,
                                }));
                            }}
                            onBlur={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: false,
                                }));
                            }}
                            control={control}
                            name={'companyType'}
                            // placeholder={'Company Type'}
                            MenuProps={{
                                classes: {
                                    paper: styles.Dropdownpaper,
                                    list: styles.muiMenuList,
                                    select: styles.selectClassName,
                                },
                                id: 'companyTypeMenu'
                            }}
                            onFocusKeyDown={() => {
                                setIsAddressContainerClicked(true)
                            }}
                            className={styles.selectDropdown}
                            placeholderClass={styles.placeholderTxt}
                            IconComponent={DropdownIcon}
                            items={companyTypes}
                            renderValue={(value: string) => {
                                const selectedType = companyTypes.find(type => type.value === value);
                                return (
                                    <span>
                                        {selectedType?.title}
                                    </span>
                                );
                            }}

                        />
                    </span>
                </div>

                {/* COMPANY HQ ADDRESS */}
                <div className={ clsx(styles.formGroupInput, styles.companyHQAddressContainer)}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyAddress && styles.focusLbl)} htmlFor="companyAddress">
                            Company Address
                        </label>
                    </span>
                    <span className={clsx(styles.col1, styles.locationAddressContainer)}>
                        {
                            (isAddressContainerClicked || errors?.companyAddress) ? (
                            <div className={clsx(styles.customAddressContainer)} ref={addressContainerRef}>
                                <span className={clsx(styles.addresInputMain)}>
                                 <InputWrapper>
                                    <CustomTextField
                                        className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line1 && styles.error)}
                                        type='text'
                                        register={register('companyAddress.line1')}
                                        placeholder='Address 1'
                                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                            register('companyAddress.line1').onBlur(e);
                                            handleInputBlur('companyAddress')
                                        }}
                                        onFocus={() => handleInputFocus('companyAddress')}
                                        errorInput={errors?.companyAddress?.line1}
                                        inputRef={(e: any) => {
                                            line1InputRef.current = e;
                                          }}
                                    />
                                </InputWrapper>
                                </span>
                              
                                 <span className={clsx(styles.addresInputMain)}>
                                      <InputWrapper>
                                    <CustomTextField
                                        className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line2 && styles.error)}
                                        type='text'
                                        // autoFocus={true}
                                        register={register('companyAddress.line2')}
                                        placeholder='Address 2'
                                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                            register('companyAddress.line2').onBlur(e);
                                            handleInputBlur('companyAddress')
                                        }}
                                        onFocus={() => handleInputFocus('companyAddress')}
                                        errorInput={errors?.companyAddress?.line2}
                                    />
                                </InputWrapper>

                                 </span>
                            

                                <span className={styles.zipInputContainer}>
                                    <span className={clsx(styles.col1,styles.addresInputMain)}>
                                        <InputWrapper>
                                            <CustomTextField
                                                className={clsx(styles.inputCreateAccount, errors?.companyAddress?.city && styles.error)}
                                                type='text'
                                                register={register('companyAddress.city')}
                                                placeholder='City'
                                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                    register('companyAddress.city').onBlur(e);
                                                    handleInputBlur('companyAddress')
                                                }}
                                                onFocus={() => handleInputFocus('companyAddress')}
                                                errorInput={errors?.companyAddress?.city}
                                            />
                                        </InputWrapper>
                                    </span>
                                    <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                                        <Controller
                                            name="companyAddress.state"
                                            control={control}
                                            render={({ field }) => (
                                                <>
                                                <SingleStateSelector
                                                    states={states.map((state: any) => ({ state_code: state.code }))}
                                                    value={field.value}
                                                    onChange={(stateCode) => {
                                                        const selectedState = states.find((state: any) => state.code === stateCode);
                                                        if (selectedState) {
                                                        field.onChange(selectedState.id);
                                                        setValue('companyAddress.stateCode', selectedState.code);
                                                        // Clear any exis ting errors for the state field
                                                        if (errors?.companyAddress?.state) {
                                                            clearErrors('companyAddress.state');
                                                        }
                                                        // Trigger validation after setting the value
                                                        setTimeout(() => {
                                                            trigger('companyAddress.state');
                                                        }, 0);
                                                        } else {
                                                        console.error('State not found for code:', stateCode);
                                                        }
                                                    }}
                                                    onBlur={() => {
                                                        field.onBlur();
                                                        handleInputBlur('companyAddress');
                                                    }}
                                                    error={!!errors?.companyAddress?.state}
                                                    placeholder="State"
                                                    stateIdToCode={(stateId) => {
                                                        const state = states.find((s: any) => s.id === stateId);
                                                        return state ? state.code : watch('companyAddress.stateCode');
                                                    }}
                                                    onFocus={() => handleInputFocus('companyAddress')}
                                                    />

                                                    </>
                                            )}
                                        />

                                    </span>
                                    <span className={clsx(styles.col3,styles.addresInputMain)}>
                                        <InputWrapper>
                                            <CustomTextField
                                                className={clsx(styles.inputCreateAccount, (errors?.companyAddress?.zip || errors?.companyAddress?.state) && styles.error)}
                                                type='text'
                                                maxLength={5}
                                                register={register('companyAddress.zip')}
                                                placeholder='Zip Code'
                                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                    register('companyAddress.zip').onBlur(e);
                                                    handleInputBlur('companyAddress');
                                                }}
                                                onFocus={() => handleInputFocus('companyAddress')}
                                                errorInput={errors?.companyAddress?.zip || errors?.companyAddress?.state}
                                                mode="wholeNumber"
                                                onKeyDown={(e) => {
                                                    if(e.key === 'Tab'){
                                                        if(!e.shiftKey){
                                                            handleShipmentAddressContainerClickAway()
                                                        }
                                                    }
                                                }}
                                            />
                                        </InputWrapper>
                                    </span>
                                </span>

                            </div>
                            ) : (
                                <div className={styles.addressDisplayContainer} onClick={() => setIsAddressContainerClicked(true)}>
                                    {
                                        (watch('companyAddress.line1') || watch('companyAddress.line2') || watch('companyAddress.city') || watch('companyAddress.state') || watch('companyAddress.zip')) ? (
                                            <div className={styles.valueDiv}>
                                                <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line1') ? `${watch('companyAddress.line1')}` : ''}</p>
                                                <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line2') ? `${watch('companyAddress.line2')}` : ''}</p>
                                                <span className={styles.lastAddressFiled}>
                                                    <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}>{watch('companyAddress.city') ? `${watch('companyAddress.city')}` : ''}</p>
                                                    <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}>{watch('companyAddress.stateCode') ? `${watch('companyAddress.stateCode')}` : ''}</p>
                                                    <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}>{watch('companyAddress.zip') ? `${watch('companyAddress.zip')}` : ''}</p>
                                                </span>
                                            </div>
                                        ) : (
                                            <span className={clsx(styles.valueDiv,styles.placeHolderDiv)} onClick={() => setIsAddressContainerClicked(true)}>Enter Ship-to address</span>
                                        )
                                    }
                                </div>
                            )
                        }
                    </span>
                </div>

                   {/* COMPANY W9 FORM */}
                   <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyW9Form && styles.focusLbl)} htmlFor="companyW9Form">
                        company w-9
                        </label>
                     
                    </span>
                    
                    <span className={styles.col1} onFocus={() => {
                        handleInputFocus('companyW9Form')
                    }} 
                    onBlur={() => {
                        handleInputBlur('companyW9Form')
                    }}
                     > 
                        {watch('companyW9Form.cerificate_url_s3') ? (
                      <>  <div className={styles.tableCell}>
                                        <div className={styles.viewCertCell}>
                                            <a href={watch('companyW9Form.cerificate_url_s3')} className={styles.viewCert} onKeyDown={(e) => {
                                                if(e.key === 'Tab'){
                                                    if(e.shiftKey){
                                                        setIsAddressContainerClicked(true)
                                                    }
                                                }
                                            }}>View</a>
                                        </div>
                                </div>
                                
                            <span className={styles.viewBtnContainer}>
                               
                                <div className={styles.viewCertBtn}>
                                  <span className={styles.viewBtn}>{watch('companyW9Form.file_name')}</span>
                                </div>
                              
                                 <button className={styles.viewBtn} onClick={irsW9FormEditHandler}><IconUpload /> </button>
                            </span></>)
                            :
                           ( <label>
                                <button onClick={irsW9FormEditHandler} className={clsx(styles.uploadText,errors?.companyW9Form?.cerificate_url_s3 && styles.errorUpload)}>
                                    <span>Upload</span>
                                    <span className={styles.uploadIcon}>
                                        <IconUpload />
                                    </span>
                                </button>
                            </label>)
                        }
                        <input className={styles.uploadFileInput} type='file'  onChange={(e) => { uploadW9Form(e); register(`companyW9Form.file`).onChange(e) }} ref={companyW9FormRef} />
                    </span>
                </div>


                {/* BILLING CONTACT NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx((isInputFocused.billingContactName) && styles.focusLbl)} htmlFor="billingContactName">
                            AP CONTACT NAME
                        </label>
                    </span>
                    <span className={clsx(styles.col1,styles.inputMain)}>
                        <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.billingContactName && styles.error)}
                                type='text'
                                register={register("billingContactName")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("billingContactName").onBlur(e);
                                    handleInputBlur('billingContactName')
                                }}
                                onFocus={() => handleInputFocus('billingContactName')}
                                errorInput={errors?.billingContactName}
                            />
                        </InputWrapper>
                    </span>
                </div>

                {/* BILLING CONTACT EMAIL */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.billingContactEmail && styles.focusLbl)} htmlFor="billingContactEmail">
                            AP CONTACT EMAIL
                        </label>
                    </span>
                    <span className={styles.col1}>
                          <EmailTagInputField
                            value={watch('billingContactEmail') ? watch('billingContactEmail').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('billingContactEmail', emails.join(','), { 
                                    shouldDirty: true, 
                                    shouldTouch: true 
                                });
                                clearErrors('billingContactEmail')
                            }}
                            placeholder=""
                            maxEmails={5}
                            register={register("billingContactEmail")}
                            error={errors?.billingContactEmail?.message}
                            onBlur={() => {
                                handleInputBlur('billingContactEmail')
                            }}
                            onFocus={() => {
                                handleInputFocus('billingContactEmail')
                            }}
                            inputBlur={() => {
                                handleInputBlur('billingContactEmail')
                            }}
                            control={control}
                        />
                    </span>
                </div>

                {/* SEND INVOICES TO */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendInvoicesTo && styles.focusLbl)} htmlFor="sendInvoicesTo">
                            SEND INVOICES TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <EmailTagInputField
                            value={watch('sendInvoicesTo') ? watch('sendInvoicesTo').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('sendInvoicesTo', emails.join(','), { 
                                    shouldDirty: true, 
                                    shouldTouch: true 
                                });
                                clearErrors('sendInvoicesTo')
                            }}
                            placeholder=""
                            maxEmails={5}
                            register={register("sendInvoicesTo")}
                            error={errors?.sendInvoicesTo?.message}
                            onBlur={() => {
                                handleInputBlur('sendInvoicesTo')
                            }}
                            inputBlur={() => {
                                handleInputBlur('sendInvoicesTo')
                            }}
                            onFocus={() => {
                                handleInputFocus('sendInvoicesTo')
                            }}
                                                         onKeyDown={(e) => {
                                 if(e.key === 'Tab'){
                                     e.preventDefault();
                                     const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
                                     if (saveButton) {
                                         saveButton.focus();
                                     }
                                 }
                             }}
                        />

                    </span>
                </div> 
            </div>
        </div>
    );
};

export default CompanyTab; 
