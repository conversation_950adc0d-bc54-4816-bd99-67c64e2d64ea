import { useGlobalStore } from '@bryzos/giss-ui-library';
import { yupResolver } from '@hookform/resolvers/yup';
import { useElements, useStripe } from '@stripe/react-stripe-js';
import { useQueryClient } from '@tanstack/react-query';
import React, { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { PAYMENT_METHODS } from 'src/renderer2/common';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import SubscriptionSetup from 'src/renderer2/component/SubscriptionDialog/components/SubscriptionSetup';
import { getPaymentSchema } from 'src/renderer2/component/SubscriptionDialog/schemas/paymentSchemas';
import usePostBuyerSettingsPayment from 'src/renderer2/hooks/usePostBuyerSettingsPayment';
import usePostUserSubscription from 'src/renderer2/hooks/usePostUserSubscription';
import { useSubscriptionStore } from 'src/renderer2/store/SubscriptionStore';

const EditLicensesDialog = () => {

  return (
    <div style={{ padding: '20px', color: 'white' }}>
      <h2>Edit Licenses</h2>
      <SubscriptionSetup currentMode={"EDIT_LICENSES"}/>
    </div>
  );
};

export default EditLicensesDialog; 