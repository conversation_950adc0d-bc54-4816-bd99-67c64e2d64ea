import React, { useEffect, useRef, useState } from 'react';
import styles from './NewSetting.module.scss';
import TabNavigation from './tabs/TabNavigation';
import CompanyTab from './tabs/CompanyTab';
import UserTab from './tabs/UserTab';
import PaymentsTab from './tabs/PaymentsTab';
import NotificationsTab from './tabs/NotificationsTab';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { useLocation } from 'react-router-dom';
import DropdownSave from './components/DropdownSave';
import ShipmentListing from './tabs/ShipmentListing/ShipmentListing';
import ResaleCertTab from './tabs/ResaleCertTab';
import SubscribeTab from '../../Subscribe/SubscribeTab';
import { useAppStore } from 'src/renderer2/store/AppStore';
import ResaleCertListing from './tabs/ResaleCert/ResaleCertListing';

const options = {
  fonts: [
    {
      cssSrc:
        'https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap',
    },
  ],
};

const NewSetting: React.FC<{
  mainWrapperRef: React.RefObject<HTMLDivElement>;
  routerContainerRef: React.RefObject<HTMLDivElement>;
}> = ({ mainWrapperRef, routerContainerRef }) => {

  const [activeTab, setActiveTab] = useState<string>('COMPANY');
  const { userData, showLoader, setShowLoader, referenceData , bryzosPayApprovalStatus ,navigationStateForNotification , setNavigationStateForNotification }: any =
    useGlobalStore();
  const [stripePromise] = useState(
    loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY)
  );
  const location = useLocation();
  const shipmentListingRef = useRef(null);
  const setMainWindowWidth = useAppStore(state => state.setMainWindowWidth);
  useEffect(() => {
    setMainWindowWidth(74.33)
    return () => {
      setMainWindowWidth(null)
    }
  }, [])

  
  const [saveFunctions, setSaveFunctions] = useState<{
    onSave: (() => void) | null;
    isDisabled: boolean;
  }>({
    onSave: null,
    isDisabled: true,
  });

  useEffect(() => {
    if(location?.state?.from === 'createPo'){
      setActiveTab('PAYMENTS');
    }
  }, [location.state?.from]);

  useEffect(() => {
    if(navigationStateForNotification?.tab){
      setActiveTab(navigationStateForNotification?.tab);
      setNavigationStateForNotification(null);
    }
  }, [navigationStateForNotification?.tab]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'COMPANY':
        return (
          <CompanyTab
            setActiveTab={setActiveTab}
            setSaveFunctions={setSaveFunctions}
          />
        );
      case 'USER':
        return (
          <UserTab
            setActiveTab={setActiveTab}
            setSaveFunctions={setSaveFunctions}
            routerContainerRef={routerContainerRef}
          />
        );
      case 'SHIPMENTS':
        return (
          <ShipmentListing shipmentListingRef={shipmentListingRef} />
        );
      case 'RESALE_CERT':
        return (
          <ResaleCertListing mainContainerRef={shipmentListingRef} />
        );
      case 'PAYMENTS':
        return (
          <Elements stripe={stripePromise} options={options}>
            <PaymentsTab
            />
          </Elements>
        );
      case 'NOTIFICATIONS':
        return (
          <NotificationsTab
            setActiveTab={setActiveTab}
            setSaveFunctions={setSaveFunctions}
          />
        );
      case 'SUBSCRIPTION':
        return (
          <Elements stripe={stripePromise} options={options}>
            <SubscribeTab 
              setActiveTab={setActiveTab}
              setSaveFunctions={setSaveFunctions}
            />
          </Elements>
        );
      default:
        return (
          <CompanyTab
          setActiveTab={setActiveTab}
          setSaveFunctions={setSaveFunctions}
        />
        );
    }
  };

  return (
    <div className={styles.newSetting} ref={shipmentListingRef}>
      
      <div className={styles.newSettingContent}>
      {Boolean(saveFunctions.onSave) && (
          <div className={styles.saveButtonContainer}>
            {
              activeTab !== 'SHIPMENTS' && activeTab !== 'RESALE_CERT' && activeTab !== 'PAYMENTS' && (
                <DropdownSave
                  onSave={saveFunctions.onSave}
                  isDisabled={saveFunctions.isDisabled}
                />
              )
            }
            
            <div className={styles.settingTitle}>settings</div>
          </div>
        )}
        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
        {renderTabContent()}
      </div>
    </div>
  );
};

export default NewSetting;
