import React from 'react';
import styles from './BlankTableRows.module.scss';

interface BlankTableRowsProps {
  count: number;
  columns: number;
  className?: string;
  rowClassName?: string;
  cellClassName?: string;
  customStyles?: React.CSSProperties;
}

/**
 * Reusable component for rendering blank table rows
 * @param count - Number of blank rows to render
 * @param columns - Number of columns in the table
 * @param className - Additional CSS class for the component
 * @param rowClassName - Additional CSS class for each row
 * @param cellClassName - Additional CSS class for each cell
 * @param customStyles - Custom inline styles
 */
const BlankTableRows: React.FC<BlankTableRowsProps> = ({
  count,
  columns,
  className = '',
  rowClassName = '',
  cellClassName = '',
  customStyles = {}
}) => {
  if (count <= 0) return null;

  const generateBlankRows = () => {
    const blankRows = [];
    
    for (let i = 0; i < count; i++) {
      const cells = [];
      for (let j = 0; j < columns; j++) {
        cells.push(
          <td 
            key={`cell-${j}`} 
            className={`${styles.blankCell} ${cellClassName}`}
            style={customStyles}
          >
            &nbsp;
          </td>
        );
      }
      
      blankRows.push(
        <tr 
          key={`blank-row-${i}`} 
          className={`${styles.blankRow} ${rowClassName}`}
        >
          {cells}
        </tr>
      );
    }
    
    return blankRows;
  };

  return (
    <>
      {generateBlankRows()}
    </>
  );
};

export default BlankTableRows; 