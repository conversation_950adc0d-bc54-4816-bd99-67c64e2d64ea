import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useStripe, useElements, CardNumberElement } from '@stripe/react-stripe-js';
import PaymentForm from './PaymentForm';
import { getPaymentSchema } from '../schemas/paymentSchemas';
import styles from '../SubscriptionDialog.module.scss';
import { useBuyerSettingStore, useGlobalStore, useSubscriptionStore } from '@bryzos/giss-ui-library';
import { commomKeys, PAYMENT_METHODS, reactQueryKeys, SUBSCRIPTION_STATUS } from 'src/renderer2/common';
import useDialogStore from '../../DialogPopup/DialogStore';
import usePostUserSubscription from 'src/renderer2/hooks/usePostUserSubscription';
import usePostBuyerSettingsPayment from 'src/renderer2/hooks/usePostBuyerSettingsPayment';
import { useQueryClient } from '@tanstack/react-query';
import usePostUpdateSubscribePayment from 'src/renderer2/hooks/usePostUpdateSubscribePayment';
import usePostUpdateSubscribeAccount from 'src/renderer2/hooks/usePostUpdateSuscribeAccount';
import { calculateSubscriptionAmount, validateAccountNumber, validateRoutingNumber } from 'src/renderer2/helper';
import InputWrapper from '../../InputWrapper';
import CustomTextField from '../../CustomTextField';
import usePutUpdateUserSubscription from 'src/renderer2/hooks/usePutUpdateUserSubscription';

const SubscriptionSetup = ({ currentMode }: { currentMode: string | undefined }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { userSubscription, isFromSetting , subscriptionsPricing , closeSubscriptionDialog } = useSubscriptionStore();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    mutateAsync: updateUserPayment
  } = usePostUpdateSubscribePayment();
  const {
    mutateAsync: updateUserAccount
  } = usePostUpdateSubscribeAccount();

  // Card completion state
  const [cardComplete, setCardComplete] = useState({
    cardNumber: false,
    cardExpiry: false,
    cardCvc: false
  });

  // Card error state
  const [cardError, setCardError] = useState<{
    cardNumber: string | null;
    cardExpiry: string | null;
    cardCvc: string | null;
  }>({
    cardNumber: null,
    cardExpiry: null,
    cardCvc: null
  });

  const [stripeError, setStripeError] = useState<string | null>(null);
  const [isCardDetailsRequired, setIsCardDetailsRequired] = useState(false);
  const { userData, setShowLoader } = useGlobalStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const {buyerSetting} = useBuyerSettingStore();
  const queryClient = useQueryClient();
  const isEditLicenseModule = currentMode === 'EDIT_LICENSES';
  const isEditPaymentModule = currentMode === 'EDIT_PAYMENT';

  // Create form with dynamic schema
  const methods = useForm<any>({
    resolver: yupResolver(getPaymentSchema(selectedPaymentMethod)),
    defaultValues: {
      numberOfLicenses: 1,
      paymentMethod: '',
    },
    mode: 'onSubmit',
  });

  const { handleSubmit, watch, setValue, setError, register, formState: { errors, isValid, isDirty, dirtyFields } } = methods;
  const numberOfLicenses = watch('numberOfLicenses');
  const [monthlyPrice, setMonthlyPrice] = useState(0);

  const {
    mutateAsync: postSaveUserSubscription
  } = usePostUserSubscription();

  const {
    mutateAsync: buyerSettingsPayment
  } = usePostBuyerSettingsPayment();

  const {
    mutateAsync: putUpdateUserSubscription
  } = usePutUpdateUserSubscription();


  useEffect(() => {
    if (userSubscription?.error_message) {
      // setShowUpdateAccount(false);
    } else if (userSubscription && subscriptionsPricing) {
      // const pricingData = calculateSubscriptionAmount(Number(userSubscription?.total_license), subscriptionsPricing);
      const pricingData = subscriptionsPricing;
      const nextPaymentCyclePrice = Number(pricingData?.price_amount)* Number(userSubscription?.licenses?.current_total);
      // const isAccountPending = (userSubscription?.status !== SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING);
      if (userSubscription?.subscription_id) {
        // setShowUpdateAccount(isAccountPending && userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED);
        // setIsUpdatePaymentModule(!isAccountPending);
        setValue('numberOfLicenses', userSubscription?.licenses?.current_total);
        setValue('existingNumberOfLicenses', userSubscription?.licenses?.current_total);
        setValue('nextPaymentCyclePrice', nextPaymentCyclePrice);
        if (userSubscription?.subscribed_emails?.length > 0) {
          setValue('emailAddress', userSubscription?.subscribed_emails);
        }
        setValue('agreeToTerms', !!userSubscription?.subscription_id);
        setValue('nextPaymentCycleDay', userSubscription?.next_payment_cycle_day);
        setValue('bankNameOrCardBrand', buyerSetting?.card?.card_display_brand ?? buyerSetting?.card?.bank_name);
      } else {
        // setShowUpdateAccount(false);
        // setIsUpdatePaymentModule(false);
        setValue('paymentMethod', '');
        setValue('numberOfLicenses', 1);
        setValue('existingNumberOfLicenses', 1);
        setValue('nextPaymentCyclePrice', 0);
        setValue('nextPaymentCycleDay', 0);
      }
      // setSubscriptionStatus(null);
    }
  }, [userSubscription, subscriptionsPricing]);

  useEffect(() => {
    if (subscriptionsPricing) {
      // const pricingData = calculateSubscriptionAmount(Number(numberOfLicenses), subscriptionsPricing);
      const pricingData = subscriptionsPricing;
      if (pricingData) {
        setMonthlyPrice(Number(pricingData.price_amount));
      }
    }
  }, [numberOfLicenses, subscriptionsPricing]);

  useEffect(() => {
    const price = (numberOfLicenses * monthlyPrice).toFixed(2);
    setValue('perUserPrice', price ?? '0.00');
  }, [numberOfLicenses, monthlyPrice])


  const isCardFormComplete = useMemo(() => {
    if (watch('paymentMethod') === PAYMENT_METHODS.CARD) {
      let commonValidation = false
      const isValidCardDetails = !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc
      if (!cardComplete.cardNumber &&
        !cardComplete.cardExpiry &&
        !cardComplete.cardCvc && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!userSubscription?.subscription_id && !!buyerSetting?.card) || (userSubscription?.status === SUBSCRIPTION_STATUS.CANCELLED))) {
        return true;
      }
      if (isEditLicenseModule || isEditPaymentModule) {
        const isUserProfileUpdated = (
          buyerSetting?.card?.first_name !== watch('cardholderFirstName') ||
          buyerSetting?.card?.last_name !== watch('cardholderLastName') ||
          buyerSetting?.card?.zipcode !== watch('billingZipCode') || userSubscription?.licenses?.current_total !== watch('numberOfLicenses')
        )
        commonValidation = (isCardDetailsRequired ?
          (!stripeError && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!cardComplete.cardCvc && !cardComplete.cardExpiry && !cardComplete.cardNumber && isUserProfileUpdated) || (cardComplete.cardCvc && cardComplete.cardExpiry && cardComplete.cardNumber)))
          :
          isUserProfileUpdated
        )
      }
      else {
        commonValidation = (isValidCardDetails &&
          !stripeError)
      }
      return (commonValidation &&
        !!watch('cardholderFirstName') &&
        !!watch('cardholderLastName') &&
        !!watch('billingZipCode') &&
        /^\d{5}$/.test(watch('billingZipCode') || '')
      )
    }
  }, [
    watch('paymentMethod'),
    cardComplete.cardNumber,
    cardComplete.cardExpiry,
    cardComplete.cardCvc,
    stripeError,
    watch('cardholderFirstName'),
    watch('cardholderLastName'),
    watch('billingZipCode'),
    isCardDetailsRequired,
    userSubscription,
    cardError,
    cardError.cardNumber,
    cardError.cardExpiry,
    cardError.cardCvc,
    cardComplete.cardNumber,
    cardComplete.cardExpiry,
    cardComplete.cardCvc,
    isDirty,
    stripeError
  ]);

  const isAchFormValid = useMemo(() => {
    if (watch('paymentMethod') !== PAYMENT_METHODS.ACH) return false;

    const routingNumber = watch('routingNumber');
    const accountNumber = watch('accountNumber');
    const reEnterAccountNumber = watch('reEnterAccountNumber');
    const accountName = watch('accountName');
    const bankName = watch('bankName');
    const accountType = watch('accountType');

    // Check if all required fields are filled and valid
    if(isEditLicenseModule || isEditPaymentModule){
        const isNewAccount = ((!!watch('accountNumber') && watch('accountNumber')?.slice(-4) !== userSubscription?.account_number) 
            || 
            userSubscription?.routing_number !== watch('routingNumber'));
        if(!isNewAccount && userSubscription?.status === SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING){
            return true;
        }
        if(isNewAccount){
            return (
                routingNumber &&
                validateRoutingNumber(routingNumber) &&
                accountNumber &&
                validateAccountNumber(accountNumber) &&
                reEnterAccountNumber &&
                accountNumber === reEnterAccountNumber &&
                accountName &&
                bankName &&
                accountType
            );
        }
        else if(watch('accountName') !== userSubscription?.account_name || watch('bankName') !== userSubscription?.bank_name || watch('accountType') !== userSubscription?.account_type.toLowerCase()){
            return (
                accountName &&
                bankName &&
                accountType
            );
        }
        return false;
    }
    else{
        return (
            routingNumber &&
            validateRoutingNumber(routingNumber) &&
            accountNumber &&
            validateAccountNumber(accountNumber) &&
            reEnterAccountNumber &&
            accountNumber === reEnterAccountNumber &&
            accountName &&
            bankName &&
            accountType
        );
    }
    
}, [watch, validateRoutingNumber, validateAccountNumber, userSubscription]);


  const isSubmitEnabled = useMemo(() => {
    if (watch('paymentMethod') === PAYMENT_METHODS.CARD) {
      return isCardFormComplete;
    } else if (currentMode === 'EDIT_LICENSES') {
      return userSubscription?.licenses?.current_total !== watch('numberOfLicenses')
    } else {
      return true
    }
  }, [isCardFormComplete, watch('paymentMethod'), userSubscription?.total_license, watch('numberOfLicenses')]);

  const handlePaymentMethodChange = (method: string) => {
    setSelectedPaymentMethod(method);
    // Reset form when payment method changes

    if (method === PAYMENT_METHODS.CARD) {
      initializeCardForm();
    } else {
      initializeAchForm();
    }
    // Reset card states when payment method changes
    setCardComplete({
      cardNumber: false,
      cardExpiry: false,
      cardCvc: false
    });
    setCardError({
      cardNumber: null,
      cardExpiry: null,
      cardCvc: null
    });
    setStripeError(null);
    setIsCardDetailsRequired(false);
  };

  const handleCardChange = useCallback((event: any, fieldName: keyof typeof cardComplete) => {
    setIsCardDetailsRequired(true);
    setCardComplete(prev => ({
      ...prev,
      [fieldName]: event.complete
    }));

    // Set or clear error based on the event
    if (event.error || (!event.empty && !event.complete)) {
      setCardError(prev => ({
        ...prev,
        [fieldName]: event.error?.message ?? 'Incomplete card details'
      }));
    } else {
      setCardError(prev => ({
        ...prev,
        [fieldName]: null
      }));
      setStripeError(null);
    }
  }, []);

  const initializeCardForm = () => {
    setValue('cardholderFirstName', buyerSetting?.card?.first_name);
    setValue('cardholderLastName', buyerSetting?.card?.last_name);
    setValue('billingZipCode', buyerSetting?.card?.zipcode);
    setValue('cardNumberLast4Digits', buyerSetting?.card?.card_number_last_four_digits);
    setValue('cardExpiry', buyerSetting?.card?.expiration_date);
    setValue('email_id', buyerSetting?.card?.email_id || userData?.data?.email_id);
  }

  const initializeAchForm = () => {
    setValue('accountName', buyerSetting?.card?.account_name);
    setValue('routingNumber', buyerSetting?.card?.routing_number);
    // setValue('accountNumber', userSubscription?.account_number);
    setValue('accountType', buyerSetting?.card?.account_type?.toLowerCase());
    setValue('bankName', buyerSetting?.card?.bank_name);
    setValue('last4AccountNumber', buyerSetting?.card?.account_number);
    // setValue('reEnterAccountNumber', userSubscription?.account_number);
  }


  const handleUpdateAccount = async (data: any) => {
    try {
      const payload = {
        "data": {
          "new_total_licenses": Number(data.numberOfLicenses) || undefined,
          "payment_method":data.paymentMethod || undefined,
        }
      }
      const response = await putUpdateUserSubscription(payload);
    } catch (err) {
      showCommonDialog(null, 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
      setShowLoader(false);
    } finally {
      setShowLoader(false);
    }
  }

  const onSubmit = async (data: any) => {
    // setShowLoader(true);
    console.log("data", data)
  
    if (data.paymentMethod === PAYMENT_METHODS.CARD && isCardFormComplete) {
      if (!stripe || !elements) {
        setStripeError("Stripe hasn't loaded yet. Please try again.");
        setShowLoader(false);
        return;
      }

      // Check if card details are complete
      if ((!buyerSetting?.card?.card_number_last_four_digits) && (!cardComplete.cardNumber || !cardComplete.cardExpiry || !cardComplete.cardCvc)) {
        setStripeError("Please complete all card details.");
        setShowLoader(false);
        return;
      }

      try {
        let _paymentMethod;
        const cardElement = elements.getElement(CardNumberElement);
        if (cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc) {
          const { paymentMethod, error: paymentMethodError } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
              email: data.email_id,
              name: `${data.cardholderFirstName} ${data.cardholderLastName}`,
              address: {
                country: 'US',
                postal_code: data.billingZipCode,
              }
            }
          });
          if (paymentMethodError) {
            setStripeError(paymentMethodError.message || 'An error occurred during payment processing');
            setShowLoader(false);
            return;
          }
          else {
            _paymentMethod = paymentMethod;
          }
        }

        const payload = {
          "data": {
            "total_licenses": Number(data.numberOfLicenses) || undefined,
            "payment_method": PAYMENT_METHODS.CARD,
            "plan_id": subscriptionsPricing?.id,
          }
        }
        const paymentSettingPayload = {
          "data": {
            "payment_method": PAYMENT_METHODS.CARD,
            "payment_details": {
              "zipcode": data.billingZipCode,
              "payment_method_id": _paymentMethod ? _paymentMethod.id : undefined,
              "first_name": data.cardholderFirstName,
              "last_name": data.cardholderLastName
            }
          }
        }

        console.log("payload", payload)
        console.log("paymentSettingPayload", paymentSettingPayload)
        console.log('watch', watch())

        const buyerSettingsPaymentResponse = await buyerSettingsPayment(paymentSettingPayload);
        if (buyerSettingsPaymentResponse.error_message) {
          setStripeError(buyerSettingsPaymentResponse.error_message);
          showCommonDialog(null, buyerSettingsPaymentResponse.error_message, null, resetDialogStore, [
            { name: commomKeys.errorBtnTitle, action: resetDialogStore }
          ]);
          setShowLoader(false);
          return;
        }

        if ( !(isEditLicenseModule || isEditPaymentModule) &&  userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED) {
          const response = await postSaveUserSubscription(payload);
          if (!response || !response?.client_secret) {
            setStripeError('No client secret received from server');
            setShowLoader(false);
            return;
          }
        }
        if (isFromSetting) {
          closeSubscriptionDialog();
        }
        queryClient.invalidateQueries([reactQueryKeys.getUserSubscription])

      } catch (err) {
        showCommonDialog(
          null,
          err?.message || 'An error occurred during payment processing',
          commomKeys.actionStatus.error,
          resetDialogStore,
          [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
        );
        setStripeError(err?.message || 'An error occurred during payment processing');
        setShowLoader(false);
        return
      }
    } else{
      const isFormValid = await methods.trigger();
    }
    console.log("data.numberOfLicenses", data.numberOfLicenses)
    console.log("userSubscription?.licenses?.current_total", userSubscription?.licenses?.current_total)
    console.log("data.paymentMethod", data.paymentMethod)
    console.log("userSubscription?.billing?.payment_method", userSubscription?.billing?.payment_method)
    if(isEditLicenseModule && (Number(data.numberOfLicenses) !== userSubscription?.licenses?.current_total || data.paymentMethod !== userSubscription?.billing?.payment_method)){
      await handleUpdateAccount(data);
    }
    setShowLoader(false);
  };
  return (
    <div className={styles.subscriptionSetup}>
      {
        !isEditPaymentModule && (
      <div className={styles.header}>
        <div className={styles.stepTitle}>STEP 1: BUY LICENSES</div>
        <div className={styles.licenseCount}>YOU HAVE 0 PAID LICENSES</div>
      </div>

        )
      }

      {/* Main Content */}
      <div className={styles.content}>
        {/* Left Panel - License Quantity */}
        {
          !isEditPaymentModule && (  
        <div className={styles.panel}>
          <div className={styles.panelTitle}>
            Enter the number of licenses you would like to purchase.
          </div>
          <div className={styles.quantityInput}>
                <InputWrapper>
                  <CustomTextField
                    className={styles.numberOfUsersInput}
                    type='text'
                    mode='wholeNumber'
                    register={register("numberOfLicenses")}
                    placeholder='No. of Users'
                  />
                </InputWrapper>
            {errors.numberOfLicenses && (
              <span className="error">{String(errors.numberOfLicenses.message)}</span>
            )}
          </div>
          <div className={styles.priceInfo}>
            Each license is $50 per month
          </div>
        </div>

          )
        }

        {/* Middle Panel - Payment Method */}

        <div className={styles.panel}>
          <FormProvider {...methods}>
            <PaymentForm
              onPaymentMethodChange={handlePaymentMethodChange}
              selectedPaymentMethod={selectedPaymentMethod}
              cardComplete={cardComplete}
              cardError={cardError}
              stripeError={stripeError}
              onCardChange={handleCardChange}
            />
          </FormProvider>
        </div>

        {/* Right Panel - Order Summary */}

        <div className={styles.panel}>
        {
          !isEditPaymentModule && ( 
          <div className={styles.orderSummary}>
            <div className={styles.summaryRow}>
              <span>Number of Licenses</span>
              <span>{numberOfLicenses}</span>
            </div>
            <div className={styles.summaryRow}>
              <span>Price per License</span>
              <span>$ {monthlyPrice}</span>
            </div>
            <div className={styles.summaryRow}>
              <span className={styles.monthlyTotal}>Monthly Total</span>
              <span className={styles.monthlyTotal}>$ {watch('perUserPrice')}</span>
            </div>
          </div>
          )
        }

          <div className={styles.disclaimer}>
            By clicking "Purchase," you agree to the terms of the{' '}
            <span className={styles.link}>Debit Authorization Agreement</span>.
          </div>

          <button
            className={styles.purchaseButton}
            onClick={() => onSubmit(watch())}
            disabled={!stripe || isProcessing || !isSubmitEnabled}
          >
            {isProcessing ? 'PROCESSING...' : 'PURCHASE'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionSetup;
