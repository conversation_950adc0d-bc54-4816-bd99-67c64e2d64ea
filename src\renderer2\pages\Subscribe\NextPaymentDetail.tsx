import React, { useState, useEffect } from 'react'
import styles from './NextPaymentDetail.module.scss'
import { calculateSubscriptionAmount } from 'src/renderer2/helper';
import { useBuyerSettingStore, useSubscriptionStore } from '@bryzos/giss-ui-library';

interface PaymentDetails {
  amount: number;
  currency: string;
  paymentDate: string;
  cardType: string;
  lastFourDigits: string;
}

interface NextPaymentDetailProps {
  initialPaymentDetails?: Partial<PaymentDetails>;
  onPaymentDetailsChange?: (details: PaymentDetails) => void;
}

const NextPaymentDetail: React.FC<NextPaymentDetailProps> = ({ 
  initialPaymentDetails,
  onPaymentDetailsChange
}) => {
  const { userSubscription , subscriptionsPricing } = useSubscriptionStore();
  const {buyerSetting} = useBuyerSettingStore();
  const [paymentDetails, setPaymentDetails] = useState<any>({
    nextPaymentCyclePrice: 0,
    nextPaymentCycleDay: 0,
    bankNameOrCardBrand: '',
    cardNumberLast4Digits: '',
    last4AccountNumber: '',
  });
  useEffect(() => {
    if(userSubscription?.subscription_id && subscriptionsPricing){
      console.log("userSubscription", userSubscription)
      const pricingData = subscriptionsPricing;
      const nextPaymentCyclePrice = Number(pricingData?.price_amount)* Number(userSubscription?.licenses?.current_total);
      setPaymentDetails({
        nextPaymentCyclePrice: nextPaymentCyclePrice,
        nextPaymentCycleDay: userSubscription?.billing?.next_billing_date,
        bankNameOrCardBrand: buyerSetting?.card?.card_display_brand ?? buyerSetting?.card?.bank_name,
        cardNumberLast4Digits: buyerSetting?.card?.card_number_last_four_digits,
        last4AccountNumber: buyerSetting?.card?.account_number,
      })
    }else{
      console.log("userSubscription", userSubscription)
    }
  }, [userSubscription])

  console.log("paymentDetails", paymentDetails)
  return (
    <div className={styles.nextPaymentContainer}>
      <div className={styles.paymentInfo}>
        <p className={styles.paymentText}>
          Your next automatic payment is for{' '}
          <span className={styles.amount}>
            {paymentDetails.nextPaymentCyclePrice}
          </span>{' '}
          on {paymentDetails.nextPaymentCycleDay}.
        </p>
        <p className={styles.cardInfo}>
          Auto-Debit from {paymentDetails.bankNameOrCardBrand} ending in {paymentDetails.cardNumberLast4Digits}
        </p>
      </div>
    </div>
  )
}

export default NextPaymentDetail