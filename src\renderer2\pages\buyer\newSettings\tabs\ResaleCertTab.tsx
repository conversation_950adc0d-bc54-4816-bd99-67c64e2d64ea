import React, { useEffect, useRef, useState } from 'react'
import styles from './ShipmentTab.module.scss'
import { clsx } from 'clsx'
import { ReactComponent as IconUpload } from '../../../../assets/New-images/icon-upload.svg';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { v4 as uuidv4 } from 'uuid';
import { prefixUrl } from 'src/renderer2/common';
import axios from 'axios';
import { CustomMenu } from '../../CustomMenu';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { resaleCertSchema } from '../schemas/resaleCert';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import { MultiStateSelector } from '../components/StateSelector';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/New-Image-latest/shipment-popup-close.svg';

const ExpirationMenuPropsBottom = {
    classes: {
        paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown),
        list: styles.muiMenuList,
        select: styles.selectClassName,
    },
    anchorOrigin: {
        vertical: 27,
        horizontal: "left"
    },
    transformOrigin: {
        vertical: "top",
        horizontal: "left"
    },
}


const ResaleCertTab = ({ closeDialog, states, resaleExpiration }: { closeDialog: () => void, states: any[], resaleExpiration: any[] }) => {
    const {
        register,
        handleSubmit,
        clearErrors,
        setError,
        setValue,
        reset,
        watch,
        control,
        getValues,
        trigger,
        resetField,
        formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
        getFieldState,
    } = useForm({
        resolver: yupResolver(resaleCertSchema),
        mode: 'onBlur',
    });

    const [isInputFocused, setIsInputFocused] = useState<any>({
        addNewCert: false,
    });
    const { userData, setShowLoader }: any = useGlobalStore()
    const resaleCertFileRef = useRef<any>(null)
    const { mutateAsync: saveUserSettings } = useSaveUserSettings();

    const resaleCertEditHandler = () => {
        resaleCertFileRef?.current?.click();
    }

    const uploadResaleCertFile = async (e: any) => {
        const file = e.target.files[0]
        setValue("resaleCertFile", file)

        if (file) {
            // setValue(`resaleCertificateList.${i}.state_id`, '');
            // setValue(`resaleCertificateList.${i}.expiration_date`, '');
            // setValue(`resaleCertificateList.${i}.status`, null);
            let index = file.name.length - 1;
            for (; index >= 0; index--) {
                if (file.name.charAt(index) === '.') {
                    break;
                }
            }
            const ext = file.name.substring(index + 1, file.name.length);

            const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.resaleCertPrefix + '-' + uuidv4() + '.' + ext;

            const payload = {
                data: {
                    "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_RESALE_CERT_BUCKET_NAME,
                    "object_key": objectKey,
                    "expire_time": 300

                }
            }
            let setCertUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
            axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
                .then(response => {
                    const signedUrl = response.data.data;
                    axios.put(signedUrl, file)
                        .then(async (response) => {
                            if (response.status === 200) {
                                setValue("resaleCertFileUrl", setCertUrl)
                                // showCommonDialog(commomKeys.uploadSuccessful, buyerSettingConst.uploadCertDialogContent, commomKeys.actionStatus.success, resetDialogStore, [{ name: commomKeys.successBtnTitle, action: resetDialogStore }])
                            }
                        })
                        .catch(error => {
                            console.error(error)
                        }
                        );
                })
                .catch(error => {
                    console.error(error)
                }
                );

        }
    }

    const handleSaveResaleCert = async () => {
        setShowLoader(true)
        let payload = [];
        payload.push({
            cerificate_url_s3: watch("resaleCertFileUrl"),
            expiration_date: watch("expiration_date"),
            file_name: watch("resaleCertFile")?.name,
            state_id: watch("state_list"),
            status: null,
        })
        try {
            await saveUserSettings({ route: 'user/buyer/settings/resale-certificate', data: payload })
            reset()
            closeDialog()
        } catch (err) {
            console.error(err)
        } finally {
            setShowLoader(false)
        }
    }

    return (
        <div className={clsx(styles.shipmentTabContentContainer)}>
            <div className={styles.shipmentTabContentHeader}>
                <h2>ADD NEW CERTIFICATE</h2>
                <button onClick={closeDialog}>Cancel <CloseIcon /></button>
            </div>
            <div className={styles.shipmentTabContentBody}>
                <div className={styles.shipmentTabContent}>
                    <div className={styles.shipmentTabContentTitle}>
                        RESALE CERTIFICATE
                    </div>
                    <div className={styles.shipmentTabContentValue}>
                        {
                            watch("resaleCertFileUrl") ? (
                                <div className={styles.resaleCertFileContainer}>
                                    <a href={watch('resaleCertFileUrl')} className={styles.viewCert}>View</a>
                                    <span>
                                        {watch("resaleCertFile")?.name}
                                    </span>
                                    <span className={styles.uploadIcon} onClick={resaleCertEditHandler}>
                                        <IconUpload />
                                    </span>
                                </div>
                            ) : (
                                <label>
                                    <button onClick={resaleCertEditHandler} className={styles.uploadCert} autoFocus={true}><span>Upload</span>
                                        <span className={styles.uploadIcon}>
                                            <IconUpload />
                                        </span>
                                    </button>
                                </label>

                            )
                        }
                    </div>
                </div>
                <div className={styles.shipmentTabContent}>
                    <div className={styles.shipmentTabContentTitle}>
                        STATE
                    </div>
                    <div className={styles.shipmentTabContentValue}>
                        <Controller
                            name="state_list"
                            control={control}
                            render={({ field }) => {
                                // Convert state IDs to state codes for display
                                const stateCodes = (field.value || []).map((stateId: any) => {
                                    const state = states.find((s: any) => s.id === stateId);
                                    return state?.code;
                                }).filter(Boolean);

                                return (
                                    <MultiStateSelector
                                        states={states.map((state: any) => ({ state_code: state.code }))}
                                        value={stateCodes}
                                        onChange={(stateCodes) => {
                                            // Convert state codes to state IDs for form storage
                                            const stateIds = stateCodes.map(stateCode => {
                                                const state = states.find((s: any) => s.code === stateCode);
                                                return state?.id;
                                            }).filter(Boolean);
                                            field.onChange(stateIds);
                                        }}
                                        onBlur={field.onBlur}
                                        error={!!errors?.state_list}
                                        placeholder="SELECT FROM DROPDOWN"
                                    />
                                );
                            }}
                        />
                    </div>
                </div>
                <div className={styles.shipmentTabContent}>
                    <div className={styles.shipmentTabContentTitle}>
                        EXPIRATION
                    </div>
                    <div className={styles.shipmentTabContentValue}>
                        <CustomMenu
                            control={control}
                            name={`expiration_date`}
                            placeholder={'SELECT FROM DROPDOWN'}
                            IconComponent={DropdownIcon}
                            MenuProps={ExpirationMenuPropsBottom}
                            items={resaleExpiration}
                            className={clsx('selectUploadCertDropdown', errors?.expiration_date && styles.borderOfError)}
                        />
                    </div>
                </div>
                <input className={styles.uploadFileInput} {...register(`resaleCertFile`)} type='file' onChange={(e) => { uploadResaleCertFile(e); register(`resaleCertFile`).onChange(e) }} ref={resaleCertFileRef} />

            </div>
            <div className={styles.footerContainer}>
                <button className={styles.saveBtnResaleCert} disabled={!isValid || watch("state_list")?.length < 1} onClick={handleSaveResaleCert} >Submit</button>
            </div>
        </div>
    )
}

export default ResaleCertTab
