import { commomKeys, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import React, { useEffect, useState } from 'react'
import styles from './ResaleCertListing.module.scss';
import { Dialog, Tooltip } from '@mui/material';
import ResaleCertTab from '../ResaleCertTab';
import { defaultResaleCertificateLine } from 'src/renderer2/common';
import clsx from 'clsx';
import axios from 'axios';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { ReactComponent as DeleteIcon } from '../../../../../assets/New-images/New-Image-latest/delete-outlined-cert.svg';
import { useDynamicTableRows } from '../../../../../hooks/useDynamicTableRows';
import BlankTableRows from '../../../../../component/BlankTableRows';

const ResaleCertListing = ({ mainContainerRef }: { mainContainerRef: React.RefObject<HTMLDivElement> }) => {
    const [addNewCertDialogOpen, setAddNewCertDialogOpen] = useState(false);
    const { buyerSetting }: any = useBuyerSettingStore();
    const [states, setStates] = useState<any[]>([])
    const [resaleExpiration, setResaleExpiration] = useState([]);
    const { referenceData, setShowLoader } = useGlobalStore()
    const [resaleCertificateList, setResaleCertificateList] = useState<any[]>([])
    const resaleCertLength = buyerSetting?.resale_certificate?.length || 0
    const { showCommonDialog, resetDialogStore }: any = useDialogStore();

    // Use the reusable hook for dynamic row calculation
    const { containerRef, blankRowsCount } = useDynamicTableRows(resaleCertificateList.length, {
        headerHeight: 46,
        rowHeight: 61,
        minRows: 1
    });

    useEffect(() => {
        if (buyerSetting?.resale_certificate) {
            let resaleCertificateList = [...defaultResaleCertificateLine];
            const resaleCertificate = buyerSetting.resale_certificate;
            if (resaleCertificate.length !== 0) {
                resaleCertificate.forEach((data: any, i: any) => {
                    data.uploadCertProgress = false;
                    resaleCertificateList[i] = data;
                });
            }
            setResaleCertificateList(resaleCertificateList);
        }
    }, [buyerSetting])

    useEffect(() => {
        if (referenceData) {
            setStates(referenceData.ref_states);
            let expiresData: any[] = [];
            referenceData?.ref_resale_cert_expiration.map((expiration: any) => {
                const expireData = {
                    title: expiration.expiration_display_string,
                    value: expiration.expiration_value
                }
                return expiresData = [...expiresData, expireData];
            })
            setResaleExpiration(expiresData);
        }
    }, [referenceData]);

    const handleAddressDialog = (isCreateNewAddress: boolean, shipmentDetails: any = null) => {
        setAddNewCertDialogOpen(true);
    }

    const openDeletePopup = (cert_id: any, index: any) => {
        showCommonDialog(commomKeys.warning, 'Are you sure you want to delete ?', null, resetDialogStore, [{ name: commomKeys.yes, action: () => deleteCerti(cert_id, index) },{ name: commomKeys.no, action: resetDialogStore }]);
    }

    const deleteCerti = (cert_id: any, index: any) => {
        setShowLoader(true)
        const payload = {
            data: {
                cert_id: cert_id,
            },
        };
        axios.post(import.meta.env.VITE_API_SERVICE + `/user/deleteResaleCert`, payload)
            .then(response => {
                if (response.data.data.error_message) {
                    showCommonDialog(null, response.data.data.error_message, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                }
                else {
                    resetDialogStore()
                }
            })
            .catch(error => {
                console.error('Error deleting file:', error);
            }).finally(() => {
                setShowLoader(false)
            })
    };


    return (
        <div className={styles.container} >
            <div className={styles.navigationSection}>

                {/* Add New Cert button */}
                <button className={styles.createButton} onClick={() => handleAddressDialog(true)} autoFocus={true}>
                    Add New Cert
                    <div className={styles.createButtonIcon}>
                        +
                        {/* <PlusIcon className={styles.plusIcon} /> */}
                    </div>
                </button>
            </div>

            {/* Resale Certificate Table */}
            <div className={styles.tableCard} ref={containerRef}>
                <table className={styles.table}>
                    <thead className={styles.tableHeader}>
                        <tr className={styles.tableHeaderRow}>
                            <th className={styles.tableHeaderCell}>
                                Certificate
                            </th>
                            <th className={styles.tableHeaderCell}>
                                STATE
                            </th>
                            <th className={styles.tableHeaderCell}>
                                EXPIRATION
                            </th>
                            <th className={styles.tableHeaderCell}>
                                STATUS
                            </th>
                            <th className={styles.tableHeaderCell} style={{ width: '80px' }}></th>
                        </tr>
                    </thead>
                    <tbody className={styles.tableBody}>
                        {resaleCertificateList.map((certificate: any, index: number) => (
                            <React.Fragment key={certificate.location_nickname || index}>
                                <tr className={styles.tableRow}>
                                    <td className={styles.tableCell}>
                                        {certificate?.cerificate_url_s3 &&
                                            <div className={styles.viewCertCell}>
                                                <a href={certificate?.cerificate_url_s3} className={styles.viewCert} onKeyDown={
                                                    (e) => {
                                                        if (e.key === 'Tab' && index === resaleCertLength - 1) {
                                                            if (!e.shiftKey) {
                                                                e.preventDefault();
                                                                const saveButton = document.getElementById('COMPANY') as HTMLButtonElement;
                                                                if (saveButton) {
                                                                    saveButton.focus();
                                                                }
                                                            }
                                                        }
                                                    }
                                                }>View Certificate</a>
                                            </div>
                                        }
                                    </td>
                                    <td className={`${styles.tableCell} ${styles.addressCell}`}>
                                        {
                                            certificate?.state_code?.length > 1 ? certificate?.state_code?.join(', ') : certificate?.state_code
                                        }
                                    </td>
                                    <td className={styles.tableCell}>
                                        {resaleExpiration.find(exp => exp.value === certificate.expiration_date)?.title || certificate.expiration_date}
                                    </td>
                                    <td className={`${styles.tableCell} ${styles.contactCell}`}>
                                        <span className={clsx(
                                            styles.statusText,
                                            certificate.status === 'Approved' && styles.statusApproved,
                                            certificate.status === 'Pending' && styles.statusPending,
                                            certificate.status === 'Rejected' && styles.statusRejected,
                                            certificate.status === 'Expired' && styles.statusRejected
                                        )}>
                                            {certificate.status === "Pending" ? "Pending Review" : certificate.status}
                                        </span>
                                    </td>
                                    <td className={`${styles.tableCell} ${styles.emailCell}`}>
                                        <div className={styles.viewCertCell}>
                                            {certificate?.id && !!certificate.is_deletable && certificate?.status !== 'Pending' &&
                                                <button onClick={() => openDeletePopup(certificate?.id, index)} className={styles.deleteCertBtn}><DeleteIcon /></button>
                                            }
                                        </div>                </td>
                                </tr>
                            </React.Fragment>
                        ))}
                        
                        {/* Use the reusable BlankTableRows component */}
                        <BlankTableRows 
                            count={blankRowsCount}
                            columns={5}
                            rowClassName={styles.tableRow}
                            cellClassName={styles.tableCell}
                        />
                    </tbody>
                </table>
            </div>
            <Dialog
                open={addNewCertDialogOpen}
                onClose={(event) => setAddNewCertDialogOpen(false)}
                transitionDuration={100}
                disableScrollLock={true}
                container={mainContainerRef.current}

                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(0, 0, 0, 0.23)',
                    border: '1px solid transparent',
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0,
                        width: '100%',
                        maxWidth: '761px',
                        borderRadius: '16px',
                        boxShadow: '0 0 67.4px 4px #000',
                        backgroundColor: '#222329',
                    }
                }}
                hideBackdrop
                classes={{
                    root: styles.customeAddressPopup,
                    paper: styles.dialogContent
                }}
            >
                <ResaleCertTab closeDialog={() => setAddNewCertDialogOpen(false)} states={states} resaleExpiration={resaleExpiration} />
            </Dialog>

        </div>
    )
}

export default ResaleCertListing
