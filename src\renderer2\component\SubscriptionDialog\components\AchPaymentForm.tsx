import React from 'react';
import { useFormContext } from 'react-hook-form';
import styles from './SubscriptionSetup.module.scss';
import InputWrapper from '../../InputWrapper';
import CustomTextField from '../../CustomTextField';
import { CustomMenu } from 'src/renderer2/pages/buyer/CustomMenu';
import { Fade } from '@mui/material';
import { ReactComponent as DropdownIcon } from '../../../assets/New-images/Dropdpown_Up_Arrow.svg';
import { validateAccountNumber, validateRoutingNumber } from 'src/renderer2/helper';

const AchPaymentForm: React.FC = () => {
  const { register, formState: { errors }, control, watch, setValue, setError, clearErrors } = useFormContext();

  const accountType = [
    { title: 'Checking', value: 'checking' },
    { title: 'Savings', value: 'savings' }
  ];



  // Handle input change for bank account fields with real-time validation
  const handleBankAccountInputChange = (field: string, value: string): void => {
    setValue(field as any, value);

    // Perform real-time validation
    // if (field === 'routingNumber') {
    //   if (value && !validateRoutingNumber(value)) {
    //     setError(field as any, {
    //       type: 'manual',
    //       message: value.length !== 9
    //         ? 'Routing number must be exactly 9 digits'
    //         : 'Invalid routing number format'
    //     });
    //   } else {
    //     clearErrors(field as any);
    //   }
    // } else if (field === 'accountNumber') {
    //   if (value && !validateAccountNumber(value)) {
    //     setError(field as any, {
    //       type: 'manual',
    //       message: !/^\d+$/.test(value)
    //         ? 'Account number must contain only digits'
    //         : value.length < 8
    //           ? 'Account number must be at least 8 digits'
    //           : 'Account number must not exceed 17 digits'
    //     });
    //   } else {
    //     clearErrors(field as any);

    //     // Also validate reEnterAccountNumber if it exists
    //     const reEnterValue = watch('reEnterAccountNumber');
    //     if (reEnterValue && reEnterValue !== value) {
    //       setError('reEnterAccountNumber', {
    //         type: 'manual',
    //         message: 'Account numbers must match'
    //       });
    //     } else if (reEnterValue) {
    //       clearErrors('reEnterAccountNumber');
    //     }
    //   }
    // } else if (field === 'reEnterAccountNumber') {
    //   const accountNumber = watch('accountNumber');
    //   if (value && accountNumber && value !== accountNumber) {
    //     setError(field as any, {
    //       type: 'manual',
    //       message: 'Account numbers must match'
    //     });
    //   } else {
    //     clearErrors(field as any);
    //   }
    // }
  };

  console.log("errors", errors)
  console.log("watch", watch())

  return (
    <div className={styles.paymentDetailsContainer}>
      <div className={styles.paymentDetails}>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="Name of Account"
            className={styles.formField}
            register={register('accountName')}
            errorInput={errors.accountName?.message}
            aria-label="Name of Account"
          />
        </InputWrapper>
        <div className={styles.accountTypeDropdown}>
          <div>
            <CustomMenu
              control={control}
              name="accountType"
              items={accountType}
              placeholder="Account Type"
              className={styles.paymentMethodSelect}
              MenuProps={{
                TransitionComponent: Fade,
                disablePortal: true,

                classes: {
                  paper: `${styles.dropdownList} ${styles.dropListAccountType} ${styles.menuSlideUp}`,
                  list: styles.muiMenuList,
                },
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
                transformOrigin: {
                  vertical: 'top',
                  horizontal: 'left',
                },
              }}
              IconComponent={DropdownIcon}
            />
          </div>
        </div>
      </div>
      <div className={styles.paymentDetails}>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="Routing Number (ABA)"
            className={styles.formField}
            register={{
              ...register('routingNumber'),
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('routingNumber', e.target.value)
            }}
            errorInput={errors.routingNumber?.message}
            aria-label="Routing Number (ABA)"
            maxLength={9}
          />
        </InputWrapper>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="Bank / Financial Institution"
            className={styles.formField}
            register={register('bankName')}
            errorInput={errors.bankName?.message}
            aria-label="Bank / Financial Institution"
          />
        </InputWrapper>
      </div>
      <div className={styles.paymentDetails}>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder={watch('last4AccountNumber') ? `****${watch('last4AccountNumber')}` : "Account Number"}
            className={styles.formField}
            register={{
              ...register('accountNumber'),
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('accountNumber', e.target.value)
            }}
            errorInput={errors.accountNumber?.message}
            aria-label="Account Number"
            maxLength={17}
          />
        </InputWrapper>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="Re-Enter Account Number"
            className={styles.formField}
            register={{
              ...register('reEnterAccountNumber'),
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('reEnterAccountNumber', e.target.value)
            }}
            errorInput={errors.reEnterAccountNumber?.message}
            aria-label="Re-Enter Account Number"
            maxLength={17}
          />
        </InputWrapper>
      </div>
    </div>
  );
};

export default AchPaymentForm; 