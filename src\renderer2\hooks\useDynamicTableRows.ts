import { useEffect, useRef, useState } from 'react';

interface UseDynamicTableRowsOptions {
  headerHeight?: number;
  rowHeight?: number;
  minRows?: number;
  containerHeight?: string;
  minContainerHeight?: string;
}

interface UseDynamicTableRowsReturn {
  totalRows: number;
  containerRef: React.RefObject<HTMLDivElement>;
  blankRowsCount: number;
}

/**
 * Custom hook for dynamically calculating table rows based on container height
 * @param dataLength - Number of actual data rows
 * @param options - Configuration options for the hook
 * @returns Object containing totalRows, containerRef, and blankRowsCount
 */
export const useDynamicTableRows = (
  dataLength: number,
  options: UseDynamicTableRowsOptions = {}
): UseDynamicTableRowsReturn => {
  const {
    headerHeight = 46,
    rowHeight = 61,
    minRows = 1,
    containerHeight = 'calc(100vh - 200px)',
    minContainerHeight = '400px'
  } = options;

  const [totalRows, setTotalRows] = useState(minRows);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const calculateTotalRows = () => {
      if (containerRef.current) {
        const containerHeight = containerRef.current.clientHeight;
        const availableHeight = containerHeight - headerHeight;
        const calculatedRows = Math.floor(availableHeight / rowHeight);
        setTotalRows(Math.max(calculatedRows, minRows));
      }
    };

    // Calculate on mount and window resize
    calculateTotalRows();
    window.addEventListener('resize', calculateTotalRows);
    
    // Use ResizeObserver for more accurate container size changes
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver(calculateTotalRows);
      resizeObserver.observe(containerRef.current);
      
      return () => {
        resizeObserver.disconnect();
        window.removeEventListener('resize', calculateTotalRows);
      };
    }
  }, [headerHeight, rowHeight, minRows]);

  const blankRowsCount = Math.max(0, totalRows - dataLength);

  return {
    totalRows,
    containerRef,
    blankRowsCount
  };
}; 