.paymentMethodContainer{
  height:calc(100vh - 175px);
  display: flex;
  flex-direction: column;
    .paymentMethodHeader{
      font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #dbdcde;
      padding: 25px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;

      svg {
        position: absolute;
        right: 0;
        top: -30px;
      }
    }
    .paymentMethodBody{
      flex: 1;
      position: relative;
      .paymentMethodBodyContainer {
        position: absolute;
        inset: 0;
        overflow: auto;
      padding-right: 10px;

        &::-webkit-scrollbar {
          width: 5px;
          height: 6px;
        }
    
        &::-webkit-scrollbar-thumb {
          background:
            #9da2b2;
          border-radius: 50px;
        }
    
        &::-webkit-scrollbar-track {
          background: transparent;
        }

      }
    }

    .paymentMethodContent{
      .paymentMethodItemHeader{
        display: flex;
        width: 100%;
        padding: 8px 0px;
        .headerItem{
          font-family: Syncopate;
          font-size: 20px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: -0.8px;
          text-align: left;
          color: #fff;
          flex: 1;
          display: flex;
          align-items: center;
        }
        .headerItemBtn{
          display: flex;
          justify-content: right;
          gap: 8px;
          flex: 1;
          align-items: center;
          padding-left: 16px;
          .headerItemStatus {
            font-family: Syncopate;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.48px;
            text-align: left;
            color: #32ff6c;
            margin-right: auto;
          }
          .headerItemStatusPending {
            font-family: Syncopate;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.48px;
            text-align: left;
            color: #ffca0e;
            margin-right: auto;
          }
          .onCreditHold {
            font-family: Syncopate;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.48px;
            text-align: left;
            color: #ff4848;
            margin-right: auto;
          }
          .headerItemStatusRestricted {
            font-family: Syncopate;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.48px;
            text-align: left;
            color: #ff4848;
            margin-right: auto;
          }
          .headerBtn{
            padding: 8px 16px;
            border-radius: 500px;
            background-color: rgba(255, 255, 255, 0.04);
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: left;
            color: #71737f;
            &:hover{
              color: #fff;
            }
          }
          .headerBtn1{
            padding: 8px 16px;
            border-radius: 500px;
            background-color: #2abcfb;
            color: #0f0f14;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: left;
            &:disabled{
              background-color: rgba(255, 255, 255, 0.04);
              color: #71737f;
              &:hover{
                background-color: rgba(255, 255, 255, 0.04);
                color: #71737f;
              }
            }
          }
        }
      }
      .paymentMethodContain{
        display: flex;
        width: 100%;
        border-top: solid 1px rgba(255, 255, 255, 0.07);
        padding: 8px 0px;
        .paymethodContainTitle{
          flex: 1;
          font-family: Syncopate;
          font-size: 18px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: -0.72px;
          text-align: left;
          color: #71737f;
          text-transform: uppercase;
          display: flex;
          align-items: center;
          padding-left: 16px;
        }
        .paymethodContainValue{
          flex: 1;
          display: flex;
          gap: 12px;
        }
      }
    }
  }
  .inputCreateAccount {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 6px 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #fff;
    transition: background 0.1s;
    outline: none;

    &.arBryzosCom{
      color: rgba(255, 255, 255, 0.4);
      &:focus{
         background: rgba(255, 255, 255, 0.04);
         color: rgba(255, 255, 255, 0.4);
      }
     }

    &.sendInvoiceEmailInput{
      text-overflow: ellipsis;
    }

    // &.error {
    //   background: url(../../../../assets/New-images/Create-Account/error-input.svg)
    //     no-repeat;
    //   background-size: cover;
    //   box-shadow: none;

    //   &:focus {
    //     background-color: green;
    //     background: url(../../../../assets/New-images/Create-Account/error-input.svg)
    //       no-repeat;
    //     background-size: cover;
    //     color: #fff;
    //   }
    // }

    &:disabled {
      cursor: not-allowed;
    }
  }
  .desiredCreditLineInput {
    outline: none;
    border: none;
  }
  .stripePaymentGrid {
    column-gap: 12px;
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
  }
  .stripeElement {
    width: 100%;
    height: 40px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0 12px 0 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
  
    & > div {
      width: 100%;
    }
  
    /* Extra styling to ensure the iframe is visible */
    iframe {
      opacity: 1 !important;
      height: 24px !important;
      min-height: 24px !important;
      width: 100% !important;
    }
  }
  .inputMain {
    position: relative;
    border-radius: 12px;
    z-index: 0;
    flex: 1;

    &:focus-within {
      box-shadow: inset 4px 5px 2.2px 0 #000;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 1.7px;
        background: linear-gradient(to bottom right, #1a1b20 61%, #fff 294%);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        background-clip: border-box;
        z-index: -1;
        pointer-events: none;
        left: -3px;
        top: -2px;
      }
    }
  }