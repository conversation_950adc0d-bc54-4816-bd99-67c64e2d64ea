import React, { useEffect, useMemo, useState } from 'react';
import { Dialog, DialogContent, Typography, IconButton } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../assets/images/tnc-close.svg';
import styles from './SubscriptionDialog.module.scss';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import SubscribeRightSideWindow from '../SubscribeRightSideWindow/SubscribeRightSideWindow';
import useGetSubscriptionsPricing from 'src/renderer2/hooks/useGetSubscriptionsPricing';
import SubscriptionUserMgmt from './components/SubscriptionUserMgmt';
import SubscriptionSetup from './components/SubscriptionSetup';
import { SUBSCRIPTION_STATUS } from 'src/renderer2/common';
import SubscriptionRightSidePanel from '../SubscriptionRightSidePanel/SubscriptionRightSidePanel';
import SubscribeTab from 'src/renderer2/pages/Subscribe/SubscribeTab';
import { useSubscriptionStore } from '@bryzos/giss-ui-library';


const SubscriptionDialog: React.FC = () => {
    const { subscriptionDialogOpen, setSubscriptionDialogOpen , userSubscription, isStripeLoaded , setIsFromSetting, subscriptionsPricing , closeSubscriptionDialog } = useSubscriptionStore();
    const { setLoadComponent} = useRightWindowStore()
    const [showRightSidePanel, setShowRightSidePanel] = useState(true);
    useEffect(() => {
        if(subscriptionDialogOpen){
            // setLoadComponent(<SubscribeRightSideWindow />);
        }else{
            setLoadComponent(null);
            setIsFromSetting(false);
        }
        return () => {
            setLoadComponent(null);
        }
    }, [subscriptionDialogOpen]);
    

    const handleClose = () => {
        closeSubscriptionDialog();
    };



    const subscriptionCompleted = useMemo(() => {
        return userSubscription?.status && userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED && userSubscription?.status !== SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING; 
    }, [userSubscription?.status]);

    return (
        <Dialog
            open={subscriptionDialogOpen}
            onClose={handleClose}
            transitionDuration={200}
            hideBackdrop
            disableScrollLock={true}
            style={{
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(7px)',
                WebkitBackdropFilter: 'blur(7px)',
                backgroundColor: 'rgba(0, 0, 0, 0.23)',
                border: '1px solid transparent',
            }}
            PaperProps={{
                style: {
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    margin: 0
                }
            }}
            classes={{
                root: styles.subscriptionDialog,
                paper: styles.dialogContent
            }}
        >
            <IconButton 
                className={styles.closeIcon} 
                onClick={handleClose}
                aria-label="close"
            >
                <CloseIcon />
            </IconButton>
            
            <DialogContent className={styles.dialogContent}>
                {
                   ( subscriptionDialogOpen && isStripeLoaded ) && (
                    <>
                    {/* <h1>Subscription</h1>
                    <h2 onClick={handleClose}>Close</h2> */}
                        {
                            subscriptionCompleted ? (<>
                                <SubscribeTab/>
                                {
                                    showRightSidePanel && (
                                        <SubscriptionRightSidePanel closeRightSidePanel={() => setShowRightSidePanel(false)}/>
                                    )
                                }
                            </>) : (<>
                                <SubscriptionSetup />
                                <SubscribeRightSideWindow />
                            </>)
                        }
                    </>
                    )
                }
            </DialogContent>
        </Dialog>
    );
};

export default SubscriptionDialog;
