import { create } from 'zustand';

const defaultStore   = {
    selectedIndex: -1,
    selectedObject: null,
    createPoUpdatedData: {},
}

export const useGlobalSearchStore = create((set, get) => ({
    ...defaultStore,
    setCreatePoUpdatedData: (v) => set(state => ({ createPoUpdatedData: typeof v === 'function' ? v(state.createPoUpdatedData) : v })),
    setSelectedIndex: (v) => set(state => ({ selectedIndex: typeof v === 'function' ? v(state.selectedIndex) : v })),
    setSelectedObject: (v) => set(state => ({ selectedObject: typeof v === 'function' ? v(state.selectedObject) : v })),
}));
