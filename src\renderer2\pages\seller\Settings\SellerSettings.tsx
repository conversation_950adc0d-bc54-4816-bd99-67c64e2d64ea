import React, { useEffect, useState } from 'react';
import styles from './NewSetting.module.scss';
import SearchHeader from '../../SearchHeader';
import TabNavigation from './tabs/TabNavigation';
import CompanyTab from './tabs/CompanyTab';
import UserTab from './tabs/UserTab';
import ShipmentsTab from './tabs/ShipmentsTab';
import PaymentsTab from './tabs/PaymentsTab';
import NotificationsTab from './tabs/NotificationsTab';
import axios from 'axios';
import { formatCurrency, useGlobalStore } from '@bryzos/giss-ui-library';
import useGetBuyingPreference from 'src/renderer2/hooks/useGetBuyingPreference';
import { yupResolver } from '@hookform/resolvers/yup';
import { set, useForm } from 'react-hook-form';
import { settingSchema } from './schemas';
import {
  formatPhoneNumberRemovingCountryCode,
  formatPhoneNumberWithHyphen,
} from 'src/renderer2/helper';
import {
  defaultResaleCertificateLine,
  RecevingHoursFrom,
  RecevingHoursTo,
  userRole,
} from 'src/renderer2/common';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import useGetDeliveryAddress from 'src/renderer2/hooks/useGetDeliveryAddress';
import useGetCompanyLists from 'src/renderer2/hooks/useGetCompanyLists';
import { useLocation } from 'react-router-dom';
import useGetSellerSetting from 'src/renderer2/hooks/useGetSellerSetting';

const SellerSettings: React.FC<{
  mainWrapperRef: React.RefObject<HTMLDivElement>;
}> = ({ mainWrapperRef }) => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid },
    getFieldState,
  } = useForm({
    resolver: yupResolver(settingSchema),
    mode: 'onBlur',
  });

  const [activeTab, setActiveTab] = useState<string>('COMPANY');
  const { userData, showLoader, setShowLoader, referenceData, bryzosPayApprovalStatus }: any =
    useGlobalStore();
  const [yourCompanyList, setYourCompanyList] = useState([]);
  const location = useLocation();
  const [achId, setAchId] = useState<number>(0);
  const [wireId, setWireId] = useState<number>(0);
  const [referenceDocumentId, setReferenceDocumentId] = useState('');


  const {
    data: sellerSettingData,
    isLoading: isSellerSettingDataLoading,
    isFetching: isSellerSettingDataFetching,
  } = useGetSellerSetting();

  const { data: companyListsData, isLoading: isCompanyListsDataLoading } =
    useGetCompanyLists();

  useEffect(() => {
    if (
      sellerSettingData &&
      !isSellerSettingDataFetching &&
      !isSellerSettingDataLoading && userData
    ) {
      handleSetInitialValues(sellerSettingData);
    }
  }, [
    sellerSettingData,
    userData,
    isSellerSettingDataFetching,
    isSellerSettingDataLoading,
  ]);

  
  useEffect(() => {
    if(location?.state?.from === 'sellerList'){
      setActiveTab('SHIPMENTS');
    }
  }, [location.state?.from]);


  useEffect(() => {
    if (watch('parentCompanyName') !== '' && companyListsData) {
      const companyData = companyListsData?.find(
        (companyData: any) =>
          companyData.company_name === watch('parentCompanyName')
      );
      setYourCompanyList(companyData?.client_company ?? []);
    }
  }, [watch('parentCompanyName'), companyListsData]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'COMPANY':
        return (
          <CompanyTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            getValues={getValues}
            trigger={trigger}
            clearErrors={clearErrors}
            getFieldState={getFieldState}
            yourCompanyList={yourCompanyList}
            resetField={resetField}
            dirtyFields={dirtyFields}
            setActiveTab={setActiveTab}
          />
        );
      case 'USER':
        return (
          <UserTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            trigger={trigger}
            resetField={resetField}
            dirtyFields={dirtyFields}
            setActiveTab={setActiveTab}
          />
        );
      case 'SHIPMENTS':
        return (
          <ShipmentsTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            getValues={getValues}
            trigger={trigger}
            clearErrors={clearErrors}
            resetField={resetField}
            dirtyFields={dirtyFields}
            setActiveTab={setActiveTab}
          />
        );
      case 'PAYMENTS':
        return (
           <PaymentsTab
              control={control}
              errors={errors}
              setError={setError}
              setValue={setValue}
              watch={watch}
              isDirty={isDirty}
              isValid={isValid}
              register={register}
              handleSubmit={handleSubmit}
              getValues={getValues}
              trigger={trigger}
              clearErrors={clearErrors}
              achId={achId}
              wireId={wireId}
              dirtyFields={dirtyFields} 
              referenceDocumentId={referenceDocumentId}
              resetField={resetField}
            />
        );

      default:
        return (
          <CompanyTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            getValues={getValues}
            trigger={trigger}
            clearErrors={clearErrors}
            yourCompanyList={yourCompanyList}
            resetField={resetField}
            dirtyFields={dirtyFields}
          />
        );
    }
  };

  const handleSetInitialValues = async (sellerSettings: any) => {

    //company tab
    setValue('parentCompanyName', sellerSettings?.company_name || '');
    setValue('companyDBAName', sellerSettings?.client_company || '');
    setValue('companyAddress', {
      line1: sellerSettings?.company_address?.line1 || '',
      line2: sellerSettings?.company_address?.line2 || '',
      city: sellerSettings?.company_address?.city || '',
      state: sellerSettings?.company_address?.state_id || '',
      stateCode: sellerSettings?.company_address?.state_code || '',
      zip: sellerSettings?.company_address?.zip || '',
    });
    setValue('sellerAddress', {
      line1: sellerSettings?.seller_address?.line1 || '',
      line2: sellerSettings?.seller_address?.line2 || '',
      city: sellerSettings?.seller_address?.city || '',
      state: sellerSettings?.seller_address?.state_id || '',
      stateCode: sellerSettings?.seller_address?.state_code || '',
      zip: sellerSettings?.seller_address?.zip || '',
    });
    setValue('billingContactName', sellerSettings?.billing_contact_name || '');
    setValue('billingContactEmail', sellerSettings?.billing_email_id || '');
    setValue('sendInvoicesTo', sellerSettings?.send_invoices_to || '<EMAIL>');
    setValue('sendOrderDocsTo', sellerSettings?.shipping_docs_to || '<EMAIL>');

    //usertab
    setValue('firstName', `${sellerSettings?.first_name} ${sellerSettings?.last_name}` || '');
    setValue('email', sellerSettings?.email_id || '');
    setValue(
      'phoneNumber',
      sellerSettings?.phone
        ? formatPhoneNumberWithHyphen(
          formatPhoneNumberRemovingCountryCode(sellerSettings?.phone)
        )
        : ''
    );
    
    //shipments tab
    setValue('stockingAddress', {
      line1: sellerSettings?.stocking_address?.line1 || '',
      line2: sellerSettings?.stocking_address?.line2 || '',
      city: sellerSettings?.stocking_address?.city || '',
      state: sellerSettings?.stocking_address?.state_id || '',
      stateCode: sellerSettings?.stocking_address?.state_code || '',
      zip: sellerSettings?.stocking_address?.zip || '',
    });
    setValue('orderFulfillmentStates', sellerSettings?.order_fulfillment_states || []);
    setValue('orderClaimPreferences', Boolean(sellerSettings?.order_claim_preferences) || false);

    //payments tab
    let _achId = 0;
    // let _wireId = 0;
    referenceData?.ref_pgpm_mapping.forEach((paymentMethod: any) => {
      if (paymentMethod.user_type === userRole.sellerUser.toLowerCase() && paymentMethod.payment_method === 'ACH') {
        _achId = paymentMethod.id;
        return setAchId(paymentMethod.id)
      }
      // if (paymentMethod.user_type === userRole.sellerUser.toLowerCase() && paymentMethod.payment_method === 'WIRE') {
      //   _wireId = paymentMethod.id;
      //   return setWireId(paymentMethod.id)
      // }
    })
    if (sellerSettings?.funding_settings) {
      if (sellerSettings?.funding_settings?.pgpm_mapping_id === _achId) {
        setValue('achCheckBox', true)
        setValue('bankName1', sellerSettings.funding_settings.bank_name);
        setValue('routingNo', sellerSettings.funding_settings.routing_number);
        setValue('accountNo', sellerSettings.funding_settings.account_number);
        setReferenceDocumentId(sellerSettings.funding_settings.reference_document_id)
      }
    }
    setValue('remittanceEmail', sellerSettings?.remittance_email);
    clearErrors();
    setShowLoader(false);
  };

  return (
    <div className={styles.newSetting}>
      <div className={styles.newSettingContent}>
        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
        {renderTabContent()}
      </div>
    </div>
  );
};

export default SellerSettings;
